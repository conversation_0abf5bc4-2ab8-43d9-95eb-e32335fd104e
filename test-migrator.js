#!/usr/bin/env node

/**
 * 简单的迁移器测试脚本
 * 用于验证 vue-migrator 的基本功能
 */

const path = require('path');
const fs = require('fs-extra');
const { spawn } = require('child_process');
const chalk = require('chalk');

const testProjectPath = path.join(__dirname, 'test-project');
const testProjectMigratedPath = path.join(__dirname, 'test-project-migrated');
const vueMigratorBin = path.join(__dirname, 'bin/vue-migrator.js');

async function main() {
  console.log(chalk.blue('🧪 开始测试 Vue Migrator...'));
  
  try {
    // 1. 检查测试项目是否存在
    console.log(chalk.gray('1. 检查测试项目...'));
    const testProjectExists = await fs.pathExists(testProjectPath);
    if (!testProjectExists) {
      throw new Error(`测试项目不存在: ${testProjectPath}`);
    }
    console.log(chalk.green('✅ 测试项目存在'));

    // 2. 清理之前的迁移结果
    console.log(chalk.gray('2. 清理之前的迁移结果...'));
    if (await fs.pathExists(testProjectMigratedPath)) {
      await fs.remove(testProjectMigratedPath);
    }
    console.log(chalk.green('✅ 清理完成'));

    // 3. 预先创建目标目录并复制 package.json
    console.log(chalk.gray('3. 准备目标项目...'));
    await fs.ensureDir(testProjectMigratedPath);
    await fs.copy(
      path.join(testProjectPath, 'package.json'),
      path.join(testProjectMigratedPath, 'package.json')
    );
    console.log(chalk.green('✅ 目标项目准备完成'));

    // 4. 执行迁移
    console.log(chalk.gray('4. 执行迁移...'));
    const result = await runCommand('node', [
      vueMigratorBin,
      'auto',
      testProjectPath,
      testProjectMigratedPath,
      '--skip-ai',
      '--skip-build',
      '--verbose'
    ]);

    if (result.exitCode !== 0) {
      throw new Error(`迁移失败，退出码: ${result.exitCode}`);
    }
    console.log(chalk.green('✅ 迁移执行完成'));

    // 5. 验证迁移结果
    console.log(chalk.gray('5. 验证迁移结果...'));
    await validateMigrationResult();
    console.log(chalk.green('✅ 迁移结果验证通过'));

    console.log(chalk.bold.green('\n🎉 Vue Migrator 测试成功！'));
    console.log(chalk.yellow('\n📁 迁移后的项目位于:'), testProjectMigratedPath);

  } catch (error) {
    console.error(chalk.red('\n❌ 测试失败:'), error.message);
    process.exit(1);
  }
}

/**
 * 运行命令
 */
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    console.log(chalk.gray(`   执行: ${command} ${args.join(' ')}`));
    
    const child = spawn(command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        exitCode: code,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      reject(error);
    });

    // 设置超时
    const timeout = setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error('命令执行超时'));
    }, 300000); // 5分钟超时

    child.on('close', () => {
      clearTimeout(timeout);
    });
  });
}

/**
 * 验证迁移结果
 */
async function validateMigrationResult() {
  // 验证基本文件存在
  const requiredFiles = [
    'package.json',
    'src/main.js',
    'src/App.vue'
  ];

  for (const file of requiredFiles) {
    const filePath = path.join(testProjectMigratedPath, file);
    const exists = await fs.pathExists(filePath);
    if (!exists) {
      throw new Error(`必需文件不存在: ${file}`);
    }
  }

  // 验证 package.json 内容
  const packageJsonPath = path.join(testProjectMigratedPath, 'package.json');
  const packageJson = await fs.readJson(packageJsonPath);
  
  if (!packageJson.dependencies || !packageJson.dependencies.vue) {
    throw new Error('package.json 中缺少 Vue 依赖');
  }

  // 验证迁移报告
  const reportPath = path.join(testProjectMigratedPath, 'migration-report.md');
  const reportExists = await fs.pathExists(reportPath);
  if (!reportExists) {
    throw new Error('迁移报告未生成');
  }

  // 验证迁移上下文
  const contextPath = path.join(testProjectMigratedPath, 'migration-context.json');
  const contextExists = await fs.pathExists(contextPath);
  if (!contextExists) {
    throw new Error('迁移上下文未保存');
  }

  console.log(chalk.gray('   ✓ 基本文件验证通过'));
  console.log(chalk.gray('   ✓ package.json 验证通过'));
  console.log(chalk.gray('   ✓ 迁移报告验证通过'));
  console.log(chalk.gray('   ✓ 迁移上下文验证通过'));
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = { main, validateMigrationResult };
