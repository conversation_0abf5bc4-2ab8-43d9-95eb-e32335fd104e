{"name": "vue3-runtime-test-project", "version": "2.0.0", "description": "Vue 3 runtime error test project", "private": true, "scripts": {"serve": "vue-cli-service serve --port 3000", "build": "vue-cli-service build", "dev": "vue-cli-service serve --port 3000", "build:vite": "vite build", "lint": "vue-cli-service lint"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.5.0", "vuex": "^4.1.0", "element-plus": "^2.9.0", "@vue/compiler-sfc": "^3.4.0", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "webpack": "^5.89.0", "vite": "^4.5.0", "sass": "^1.82.0", "sass-loader": "^13.3.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}