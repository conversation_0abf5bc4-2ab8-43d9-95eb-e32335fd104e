import { createStore } from 'vuex'

export default createStore({
  state: {
    count: 0,
    user: null
  },
  getters: {
    doubleCount: (state) => state.count * 2
  },
  mutations: {
    increment (state) {
      state.count++
    },
    setUser (state, user) {
      state.user = user
    }
  },
  actions: {
    increment ({ commit }) {
      commit('increment')
    },
    async fetchUser ({ commit }) {
      // 模拟异步操作
      const user = { id: 1, name: 'Test User' }
      commit('setUser', user)
    }
  },
  modules: {
  }
})
