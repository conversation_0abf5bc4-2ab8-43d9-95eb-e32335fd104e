import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '../views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/error-page',
    name: 'ErrorPage',
    component: () => import('../views/ErrorPage.vue')
  },
  {
    path: '/null-error',
    name: 'NullError',
    component: () => import('../views/NullError.vue')
  },
  {
    path: '/undefined-error',
    name: 'UndefinedError',
    component: () => import('../views/UndefinedError.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
