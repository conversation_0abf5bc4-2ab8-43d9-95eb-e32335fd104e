const fs = require('fs-extra');
const path = require('path');
const PackageJsonManager = require('../src/features/package-json/PackageJsonManager');
const PackageJsonMerger = require('../src/features/package-json/PackageJsonMerger');

describe('Package.json 集成测试', () => {
  let tempDir;
  let sourceDir;
  let targetDir;
  let workingDir;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = path.join(__dirname, 'temp', 'package-json-integration');
    sourceDir = path.join(tempDir, 'source');
    targetDir = path.join(tempDir, 'target');
    workingDir = path.join(tempDir, 'working');

    await fs.ensureDir(sourceDir);
    await fs.ensureDir(targetDir);
    await fs.ensureDir(workingDir);

    // 创建测试用的 package.json 文件
    await createTestPackageJsonFiles();
  });

  afterEach(async () => {
    // 清理临时文件
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  async function createTestPackageJsonFiles() {
    // 源项目 package.json (Vue 2 项目)
    const sourcePackageJson = {
      name: 'vue2-source-project',
      version: '1.0.0',
      description: 'Vue 2 source project for migration',
      dependencies: {
        vue: '^2.6.14',
        'vue-router': '^3.5.4',
        vuex: '^3.6.2',
        'element-ui': '^2.15.13',
        'vue-count-to': '^1.0.13',
        axios: '^0.27.2',
        lodash: '^4.17.21'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14',
        '@vue/cli-service': '^4.5.15',
        webpack: '^4.46.0'
      },
      scripts: {
        serve: 'vue-cli-service serve',
        build: 'vue-cli-service build',
        lint: 'vue-cli-service lint',
        test: 'npm run test:unit'
      }
    };

    // 目标项目 package.json (Vue 3 项目模板)
    const targetPackageJson = {
      name: 'vue3-target-project',
      version: '2.0.0',
      description: 'Vue 3 target project template',
      dependencies: {
        vue: '^3.4.0',
        'vue-router': '^4.5.0',
        vuex: '^4.1.0',
        'element-plus': '^2.9.0',
        '@vue/compiler-sfc': '^3.4.0'
      },
      devDependencies: {
        '@vue/cli-service': '^5.0.8',
        webpack: '^5.89.0',
        vite: '^4.5.0'
      },
      scripts: {
        serve: 'vue-cli-service serve',
        build: 'vue-cli-service build',
        dev: 'vite',
        'build:vite': 'vite build'
      }
    };

    // 工作目录 package.json (单项目模式)
    const workingPackageJson = {
      name: 'vue-working-project',
      version: '1.0.0',
      dependencies: {
        vue: '^2.6.14',
        'vue-router': '^3.5.4',
        'element-ui': '^2.15.13'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14'
      }
    };

    await fs.writeJson(path.join(sourceDir, 'package.json'), sourcePackageJson, { spaces: 2 });
    await fs.writeJson(path.join(targetDir, 'package.json'), targetPackageJson, { spaces: 2 });
    await fs.writeJson(path.join(workingDir, 'package.json'), workingPackageJson, { spaces: 2 });
  }

  describe('源到目标模式完整流程', () => {
    it('应该能够完成完整的源到目标迁移流程', async () => {
      const manager = new PackageJsonManager({
        sourceProjectPath: sourceDir,
        targetProjectPath: targetDir,
        sourceToTargetMode: true,
        migrationMode: true,
        preserveVue3Dependencies: true,
        enableThirdPartyMapping: true,
        verbose: false,
        dryRun: true
      });

      const result = await manager.processPackageJson();

      // 验证结果结构
      expect(result).toHaveProperty('merge');
      expect(result).toHaveProperty('upgrade');
      expect(result).toHaveProperty('totalChanges');
      expect(typeof result.totalChanges).toBe('number');

      // 验证合并结果
      expect(result.merge.success).toBe(true);
      expect(Array.isArray(result.merge.changes)).toBe(true);

      // 验证升级结果
      expect(result.upgrade).not.toBeNull();

      // 生成和验证报告
      const report = manager.generateMigrationReport(result);
      expect(report.summary.totalChanges).toBeGreaterThanOrEqual(0);
    });

    it('应该正确合并依赖', async () => {
      const merger = new PackageJsonMerger(sourceDir, targetDir, {
        preserveTargetDependencies: true,
        enableThirdPartyMapping: true,
        dryRun: true
      });

      const result = await merger.merge();
      const merged = result.mergedPackageJson;

      // 验证 Vue 3 依赖被保留
      expect(merged.dependencies.vue).toBe('^3.4.0');
      expect(merged.dependencies['vue-router']).toBe('^4.5.0');
      expect(merged.dependencies.vuex).toBe('^4.1.0');

      // 验证 Vue 2 官方依赖被跳过
      expect(merged.devDependencies).not.toHaveProperty('vue-template-compiler');

      // 验证兼容的第三方依赖被添加
      expect(merged.dependencies).toHaveProperty('axios');
      expect(merged.dependencies).toHaveProperty('lodash');

      // 验证目标项目的特有依赖被保留
      expect(merged.dependencies).toHaveProperty('element-plus');
      expect(merged.devDependencies).toHaveProperty('vite');
    });
  });

  describe('单项目模式完整流程', () => {
    it('应该能够完成单项目迁移流程', async () => {
      const manager = new PackageJsonManager({
        workingPath: workingDir,
        sourceToTargetMode: false,
        migrationMode: true,
        enableThirdPartyMapping: true,
        verbose: false,
        dryRun: true
      });

      const result = await manager.processPackageJson();

      // 验证结果结构
      expect(result).toHaveProperty('upgrade');
      expect(result).toHaveProperty('totalChanges');
      expect(typeof result.totalChanges).toBe('number');

      // 验证升级结果
      expect(result.upgrade).not.toBeNull();
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理空的 package.json', async () => {
      const emptyPackageJson = {
        name: 'empty-project',
        version: '1.0.0'
      };

      await fs.writeJson(path.join(sourceDir, 'package.json'), emptyPackageJson, { spaces: 2 });

      const merger = new PackageJsonMerger(sourceDir, targetDir, {
        dryRun: true
      });

      const result = await merger.merge();
      expect(result.success).toBe(true);
    });

    it('应该处理缺少依赖部分的 package.json', async () => {
      const noDepsPackageJson = {
        name: 'no-deps-project',
        version: '1.0.0',
        scripts: {
          start: 'node index.js'
        }
      };

      await fs.writeJson(path.join(sourceDir, 'package.json'), noDepsPackageJson, { spaces: 2 });

      const merger = new PackageJsonMerger(sourceDir, targetDir, {
        dryRun: true
      });

      const result = await merger.merge();
      expect(result.success).toBe(true);
    });

    it('应该验证迁移后的 package.json', async () => {
      const manager = new PackageJsonManager();
      const packageJsonPath = path.join(targetDir, 'package.json');

      const validation = await manager.validatePackageJson(packageJsonPath);

      expect(validation.valid).toBe(true);
      expect(validation.packageJson).not.toBeNull();
      expect(validation.packageJson.name).toBe('vue3-target-project');
    });
  });

  describe('配置选项验证', () => {
    it('应该支持不同的配置组合', async () => {
      const configs = [
        {
          preserveTargetDependencies: true,
          enableThirdPartyMapping: true
        },
        {
          preserveTargetDependencies: false,
          enableThirdPartyMapping: true
        },
        {
          preserveTargetDependencies: true,
          enableThirdPartyMapping: false
        },
        {
          preserveTargetDependencies: false,
          enableThirdPartyMapping: false
        }
      ];

      for (const config of configs) {
        const merger = new PackageJsonMerger(sourceDir, targetDir, {
          ...config,
          dryRun: true
        });

        const result = await merger.merge();
        expect(result.success).toBe(true);
      }
    });
  });

  describe('性能和稳定性', () => {
    it('应该能够处理大型 package.json 文件', async () => {
      // 创建一个包含大量依赖的 package.json
      const largeDependencies = {};
      for (let i = 0; i < 100; i++) {
        largeDependencies[`test-package-${i}`] = '^1.0.0';
      }

      const largePackageJson = {
        name: 'large-project',
        version: '1.0.0',
        dependencies: largeDependencies
      };

      await fs.writeJson(path.join(sourceDir, 'package.json'), largePackageJson, { spaces: 2 });

      const merger = new PackageJsonMerger(sourceDir, targetDir, {
        dryRun: true
      });

      const startTime = Date.now();
      const result = await merger.merge();
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // 应该在 5 秒内完成
    });
  });
});
