const path = require('path');
const fs = require('fs-extra');
const { contextManager } = require('../src/core/context/ContextManager');
const MigrationContext = require('../src/core/context/MigrationContext');
const ContextAwareComponent = require('../src/core/context/ContextAwareComponent');

describe('Context System Basic Tests', () => {
  let testProjectPath;
  let contextId;

  beforeEach(async () => {
    // 创建临时测试项目
    testProjectPath = path.join(__dirname, 'temp-basic-test');
    await fs.ensureDir(testProjectPath);

    // 创建基本的 package.json
    await fs.writeJson(path.join(testProjectPath, 'package.json'), {
      name: 'test-project',
      version: '1.0.0'
    });
  });

  afterEach(async () => {
    // 清理
    if (contextId) {
      contextManager.removeContext(contextId);
      contextId = null;
    }
    await fs.remove(testProjectPath);
  });

  test('should create migration context', () => {
    const context = new MigrationContext(testProjectPath, { mode: 'test' });

    expect(context.projectPath).toBe(testProjectPath);
    expect(context.options.mode).toBe('test');
    expect(context.project.path).toBe(testProjectPath);
    expect(context.phases.current).toBeNull();
    expect(Array.isArray(context.phases.completed)).toBe(true);
  });

  test('should manage phases', () => {
    const context = new MigrationContext(testProjectPath);

    // 开始阶段
    context.startPhase('test-phase');
    expect(context.phases.current).toBe('test-phase');

    // 完成阶段
    const result = { success: true };
    context.completePhase('test-phase', result);
    expect(context.phases.current).toBeNull();
    expect(context.phases.completed).toContain('test-phase');
    expect(context.phases.results['test-phase']).toEqual(result);
  });

  test('should track errors and warnings', () => {
    const context = new MigrationContext(testProjectPath);

    const error = new Error('Test error');
    const warning = 'Test warning';

    context.addError(error, 'test-context');
    context.addWarning(warning, 'test-context');

    expect(context.issues.errors).toHaveLength(1);
    expect(context.issues.warnings).toHaveLength(1);
    expect(context.issues.errors[0].message).toBe('Test error');
    expect(context.issues.warnings[0].message).toBe('Test warning');
  });

  test('should create and manage contexts with ContextManager', () => {
    const result = contextManager.createContext(testProjectPath, { mode: 'test' });

    contextId = result.contextId;
    const context = result.context;

    expect(typeof contextId).toBe('string');
    expect(context).toBeInstanceOf(MigrationContext);
    expect(contextManager.getContext(contextId)).toBe(context);
  });

  test('should handle context-aware components', async () => {
    class TestComponent extends ContextAwareComponent {
      constructor(options = {}) {
        super('TestComponent', options);
        this.executed = false;
      }

      async execute() {
        this.executed = true;
        return { success: true };
      }
    }

    const result = contextManager.createContext(testProjectPath);
    contextId = result.contextId;
    const context = result.context;

    const component = new TestComponent();
    await component.initialize(contextId);

    expect(component.isInitialized).toBe(true);
    expect(component.getContext()).toBe(context);
    expect(context.getTool('TestComponent')).toBe(component);

    const executeResult = await component.start();

    expect(component.executed).toBe(true);
    expect(component.state.status).toBe('completed');
    expect(executeResult.success).toBe(true);
  });

  test('should export and import context data', () => {
    const context = new MigrationContext(testProjectPath);

    // 设置一些数据
    context.setProjectInfo({ name: 'test', type: 'vue' });
    context.setConfig('testKey', 'testValue');
    context.addError(new Error('Test error'));

    // 导出数据
    const exportedData = context.export();

    expect(exportedData.project.name).toBe('test');
    expect(exportedData.config.testKey).toBe('testValue');
    expect(exportedData.issues.errors).toHaveLength(1);
  });

  test('should handle event subscriptions', (done) => {
    const result = contextManager.createContext(testProjectPath);
    contextId = result.contextId;
    const context = result.context;

    // 订阅事件
    contextManager.subscribe('phase:start', (data) => {
      try {
        expect(data.contextId).toBe(contextId);
        expect(data.phase).toBe('test-phase');
        done();
      } catch (error) {
        done(error);
      }
    }, contextId);

    // 使用 setTimeout 确保事件监听器已设置
    setTimeout(() => {
      context.startPhase('test-phase');
    }, 10);
  });

  test('should provide status summary', () => {
    const context = new MigrationContext(testProjectPath);

    context.setProjectInfo({ detectedFramework: 'Vue 2' });
    context.startPhase('test-phase');
    context.completePhase('test-phase', { success: true });
    context.addError(new Error('Test error'));

    const summary = context.getStatusSummary();

    expect(summary.project.detectedFramework).toBe('Vue 2');
    expect(summary.completedPhases).toBe(1);
    expect(summary.errorCount).toBe(1);
    expect(typeof summary.duration).toBe('number');
  });
});
