const path = require('path');
const fs = require('fs-extra');
const AutoMigrator = require('../src/app/AutoMigrator');

describe('智能文件复制功能', () => {
  let testSourceDir;
  let testTargetDir;
  let migrator;

  beforeEach(async () => {
    // 创建临时测试目录
    testSourceDir = path.join(__dirname, 'temp', 'smart-copy-source');
    testTargetDir = path.join(__dirname, 'temp', 'smart-copy-target');

    await fs.ensureDir(testSourceDir);
    await fs.ensureDir(testTargetDir);

    // 创建测试项目结构
    await createTestProject(testSourceDir);

    // 初始化迁移器
    migrator = new AutoMigrator(testSourceDir, {
      sourceToTargetMode: true,
      sourceProjectPath: testSourceDir,
      targetProjectPath: testTargetDir,
      verbose: true
    });
  });

  afterEach(async () => {
    // 清理测试目录
    await fs.remove(path.join(__dirname, 'temp'));
  });

  async function createTestProject(projectDir) {
    // 创建基本项目结构
    await fs.writeJson(path.join(projectDir, 'package.json'), {
      name: 'test-project',
      version: '1.0.0',
      dependencies: {
        'vue': '^2.6.14'
      }
    });

    // 创建 src 目录
    await fs.ensureDir(path.join(projectDir, 'src'));
    await fs.writeFile(
      path.join(projectDir, 'src', 'main.js'),
      'import Vue from "vue";\nimport App from "./App.vue";\n'
    );

    // 创建其他重要目录和文件
    await fs.ensureDir(path.join(projectDir, 'mock'));
    await fs.writeFile(
      path.join(projectDir, 'mock', 'index.js'),
      'module.exports = { "/api/test": { code: 0 } };'
    );

    await fs.ensureDir(path.join(projectDir, 'public'));
    await fs.writeFile(
      path.join(projectDir, 'public', 'index.html'),
      '<!DOCTYPE html><html><head><title>Test</title></head><body><div id="app"></div></body></html>'
    );

    await fs.writeFile(
      path.join(projectDir, 'vue.config.js'),
      'module.exports = { devServer: { port: 8080 } };'
    );

    await fs.writeFile(
      path.join(projectDir, '.env.development'),
      'VUE_APP_API_BASE_URL=http://localhost:3000'
    );
  }

  test('应该能够使用默认策略复制文件', async () => {
    // 模拟没有 AI 能力的情况
    migrator.options.aiApiKey = null;

    await migrator.smartCopyAdditionalFiles();

    // 验证默认文件是否被复制
    expect(await fs.pathExists(path.join(testTargetDir, '.env.development'))).toBe(true);
    expect(await fs.pathExists(path.join(testTargetDir, 'mock'))).toBe(true);
    expect(await fs.pathExists(path.join(testTargetDir, 'public'))).toBe(true);
  });

  test('应该能够验证文件是否适合复制', () => {
    // 测试有效文件
    expect(migrator.isValidFileForCopy('vue.config.js')).toBe(true);
    expect(migrator.isValidFileForCopy('mock')).toBe(true);
    expect(migrator.isValidFileForCopy('public/index.html')).toBe(true);

    // 测试无效文件
    expect(migrator.isValidFileForCopy('node_modules')).toBe(false);
    expect(migrator.isValidFileForCopy('src')).toBe(false);
    expect(migrator.isValidFileForCopy('package.json')).toBe(false);
    expect(migrator.isValidFileForCopy('error.log')).toBe(false);
    expect(migrator.isValidFileForCopy('.DS_Store')).toBe(false);
  });

  test('应该能够正确解析项目分析响应', () => {
    const mockResponse = `
<project_analysis>
<files_to_copy>
<item type="file">vue.config.js</item>
<item type="directory">mock</item>
<item type="file">.env.development</item>
<item type="directory">public</item>
</files_to_copy>
<reasoning>
这些文件对项目运行很重要
</reasoning>
</project_analysis>
    `;

    const result = migrator.parseProjectAnalysisResponse(mockResponse);

    expect(result).toHaveLength(4);
    expect(result).toContainEqual({ type: 'file', path: 'vue.config.js' });
    expect(result).toContainEqual({ type: 'directory', path: 'mock' });
    expect(result).toContainEqual({ type: 'file', path: '.env.development' });
    expect(result).toContainEqual({ type: 'directory', path: 'public' });
  });

  test('应该能够处理无效的 AI 响应', () => {
    const invalidResponse = '这是一个无效的响应';
    const result = migrator.parseProjectAnalysisResponse(invalidResponse);
    expect(result).toHaveLength(0);
  });
});
