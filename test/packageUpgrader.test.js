const PackageUpgrader = require('../src/features/package-json/PackageUpgrader')
const fs = require('fs-extra')
const path = require('path')
const os = require('os')

describe('PackageUpgrader', () => {
	let tempDir
	let upgrader

	beforeEach(async () => {
		tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'package-upgrader-test-'))
		upgrader = new PackageUpgrader(tempDir)
	})

	afterEach(async () => {
		if (tempDir && await fs.pathExists(tempDir)) {
			await fs.remove(tempDir)
		}
	})

	describe('buildVersionCompatibility', () => {
		it('should detect html-webpack-plugin version issues', async () => {
			// 创建测试 package.json
			const packageJson = {
				name: 'test-project',
				version: '1.0.0',
				devDependencies: {
					'html-webpack-plugin': '^4.5.0',
					'script-ext-html-webpack-plugin': '^2.1.5'
				}
			}
			await fs.writeJson(path.join(tempDir, 'package.json'), packageJson)

			await upgrader.loadConfig()
			const issues = await upgrader.buildVersionCompatibility()

			expect(issues).toHaveLength(2)
			expect(issues[0]).toMatchObject({
				type: 'critical',
				package: 'html-webpack-plugin',
				currentVersion: '^4.5.0',
				requiredVersion: '^5.6.0',
				error: 'htmlWebpackPlugin.getHooks is not a function'
			})
			expect(issues[1]).toMatchObject({
				type: 'incompatible',
				package: 'script-ext-html-webpack-plugin',
				error: 'Incompatible with html-webpack-plugin 5.x'
			})
		})

		it('should not detect issues with compatible versions', async () => {
			const packageJson = {
				name: 'test-project',
				version: '1.0.0',
				devDependencies: {
					'html-webpack-plugin': '^5.6.0'
				}
			}
			await fs.writeJson(path.join(tempDir, 'package.json'), packageJson)

			await upgrader.loadConfig()
			const issues = await upgrader.buildVersionCompatibility()

			expect(issues).toHaveLength(0)
		})
	})

	describe('handleVersionCompatibility', () => {
		it('should automatically fix critical issues', async () => {
			const packageJson = {
				name: 'test-project',
				version: '1.0.0',
				devDependencies: {
					'html-webpack-plugin': '^4.5.0'
				}
			}
			await fs.writeJson(path.join(tempDir, 'package.json'), packageJson)

			await upgrader.loadConfig()
			const result = await upgrader.handleVersionCompatibility()

			expect(result.handled).toBe(true)
			expect(result.issues).toHaveLength(1)

			// 检查 package.json 是否被更新
			const updatedPackageJson = await fs.readJson(path.join(tempDir, 'package.json'))
			expect(updatedPackageJson.devDependencies['html-webpack-plugin']).toBe('^4.5.0')
		})
	})

	describe('getCriticalDependencies', () => {
		it('should return critical dependencies from config', async () => {
			await upgrader.loadConfig()
			const criticalDeps = upgrader.getCriticalDependencies()

			expect(criticalDeps).toContainEqual(
				expect.objectContaining({
					name: 'html-webpack-plugin',
					version: '^5.6.0',
					critical: true
				})
			)
		})
	})
})
