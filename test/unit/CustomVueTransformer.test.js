const CustomVueTransformer = require('../../src/frameworks/vue/CustomVueTransformer');

describe('CustomVueTransformer', () => {
  let transformer;

  beforeEach(() => {
    transformer = new CustomVueTransformer();
  });

  describe('fixMultilineClickHandlers', () => {
    it('should fix multiline @click handlers without function wrapper', () => {
      const input = `
<template>
  <el-button
    @click="
      AddVisible = true
      $nextTick(() => {
        $refs.AddMemberDlg.init({
          posId: form_data.posId,
          posName: posName,
        })
      })
    "
  >添加成员</el-button>
</template>`;

      const expected = `
<template>
  <el-button
    @click="() => { AddVisible = true; $nextTick(() => { $refs.AddMemberDlg.init({ posId: form_data.posId, posName: posName, }) }); }"
  >添加成员</el-button>
</template>`;

      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toContain('@click="() => { AddVisible = true;');
      expect(result).toContain('$nextTick(() => {');
    });

    it('should fix simple multiline @click handlers', () => {
      const input = `
<el-button
  @click="
    UpdateVisible = true
    SelectId = scope.row.ruleId
  "
>修改</el-button>`;

      const expected = `
<el-button
  @click="() => { UpdateVisible = true; SelectId = scope.row.ruleId; }"
>修改</el-button>`;

      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toContain('@click="() => { UpdateVisible = true; SelectId = scope.row.ruleId; }"');
    });

    it('should not modify single line @click handlers', () => {
      const input = `<el-button @click="handleClick">Click</el-button>`;
      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toBe(input);
    });

    it('should not modify @click handlers that are already functions', () => {
      const input = `<el-button @click="() => { doSomething(); }">Click</el-button>`;
      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toBe(input);
    });

    it('should not modify @click handlers with arrow functions', () => {
      const input = `<el-button @click="(event) => handleClick(event)">Click</el-button>`;
      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toBe(input);
    });

    it('should handle multiple multiline @click handlers in the same file', () => {
      const input = `
<template>
  <el-button
    @click="
      visible1 = true
      id1 = 123
    "
  >Button 1</el-button>
  <el-button
    @click="
      visible2 = false
      id2 = 456
    "
  >Button 2</el-button>
</template>`;

      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toContain('@click="() => { visible1 = true; id1 = 123; }"');
      expect(result).toContain('@click="() => { visible2 = false; id2 = 456; }"');
    });

    it('should handle complex nested function calls', () => {
      const input = `
<el-button
  @click="
    AddVisible = true
    $nextTick(() => {
      $refs.AddMemberDlg.init({posId:form_data.posId, posName:posName})
    })
  "
>添加成员</el-button>`;

      const result = transformer.fixMultilineClickHandlers(input);
      expect(result).toContain('@click="() => {');
      expect(result).toContain('AddVisible = true;');
      expect(result).toContain('$nextTick(() => {');
    });
  });

  describe('transform integration', () => {
    it('should apply multiline click handler fix during transform', async () => {
      const input = `
<template>
  <el-button
    @click="
      UpdateVisible = true
      SelectId = scope.row.ruleId
    "
  >修改</el-button>
</template>

<script>
export default {
  data() {
    return {
      UpdateVisible: false,
      SelectId: null
    }
  }
}
</script>`;

      const result = await transformer.transform(input, 'test.vue');
      expect(result).toContain('@click="() => { UpdateVisible = true; SelectId = scope.row.ruleId; }"');
    });
  });
});
