const fs = require('fs-extra');
const path = require('path');
const FileFixHistoryManager = require('../../src/domain/build-fix/ai/FileFixHistoryManager');

describe('FileFixHistoryManager', () => {
  let historyManager;
  let tempDir;

  beforeEach(async () => {
    tempDir = path.join(__dirname, '..', 'temp', 'history-test');
    await fs.ensureDir(tempDir);
    
    historyManager = new FileFixHistoryManager(tempDir, {
      enablePersistence: true,
      verbose: false,
      maxHistoryPerFile: 3
    });
  });

  afterEach(async () => {
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('基本功能', () => {
    it('应该能够开始文件修复尝试', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      const context = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      
      expect(context).toHaveProperty('attemptId');
      expect(context).toHaveProperty('fileHistory');
      expect(context).toHaveProperty('previousFailures');
      expect(context).toHaveProperty('suggestedStrategies');
      expect(context).toHaveProperty('patterns');

      expect(Array.isArray(context.fileHistory)).toBe(true);
      expect(Array.isArray(context.previousFailures)).toBe(true);
      expect(Array.isArray(context.suggestedStrategies)).toBe(true);
    });

    it('应该能够记录AI调用', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      const context = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      
      historyManager.recordAICall(context.attemptId, {
        taskType: 'file-fix',
        phase: 'fix-generation',
        prompt: 'Fix this Vue component',
        response: 'Fixed component code',
        success: true,
        duration: 1500,
        strategy: 'vue-component-fix'
      });

      // 验证AI调用被记录
      const history = await historyManager.getFileHistory(filePath);
      expect(history).toHaveLength(1);
      expect(history[0].aiCalls).toHaveLength(1);
      expect(history[0].aiCalls[0].taskType).toBe('file-fix');
      expect(history[0].aiCalls[0].success).toBe(true);
    });

    it('应该能够结束修复尝试并记录结果', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      const context = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      
      const result = {
        success: true,
        newContent: '<template><div>Fixed</div></template>'
      };

      const attempt = await historyManager.endFileFixAttempt(
        context.attemptId,
        true,
        result,
        null
      );

      expect(attempt.success).toBe(true);
      expect(attempt).toHaveProperty('result');
      expect(attempt).toHaveProperty('endTime');
      expect(attempt).toHaveProperty('duration');
    });
  });

  describe('历史管理', () => {
    it('应该能够获取文件的修复历史', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      // 第一次尝试
      const context1 = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      await historyManager.endFileFixAttempt(context1.attemptId, false, null, 'Fix failed');
      
      // 第二次尝试
      const context2 = await historyManager.startFileFixAttempt(filePath, buildOutput, 2);
      await historyManager.endFileFixAttempt(context2.attemptId, true, { success: true }, null);

      const history = await historyManager.getFileHistory(filePath);
      expect(history).toHaveLength(2);
      expect(history[0].success).toBe(false);
      expect(history[1].success).toBe(true);
    });

    it('应该能够获取上一次失败信息', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      const context = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      await historyManager.endFileFixAttempt(context.attemptId, false, null, 'Import error');

      const lastFailure = await historyManager.getLastFailure(filePath);
      expect(lastFailure).not.toBeNull();
      expect(lastFailure.error).toBe('Import error');
      expect(lastFailure.attemptNumber).toBe(1);
    });

    it('应该能够推荐修复策略', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      const strategies = await historyManager.getRecommendedStrategies(filePath, buildOutput);
      expect(Array.isArray(strategies)).toBe(true);

      // 应该包含基于文件类型的默认策略
      const vueStrategy = strategies.find(s => s.strategy === 'vue-component-fix');
      expect(vueStrategy).toBeDefined();
      expect(vueStrategy).toHaveProperty('confidence');
      expect(vueStrategy).toHaveProperty('reason');
    });
  });

  describe('错误模式识别', () => {
    it('应该能够识别Vue相关错误模式', async () => {
      const buildOutput = `
        Module not found: Error: Can't resolve 'vue-router'
        export 'createRouter' (imported as 'createRouter') was not found
      `;
      
      const patterns = historyManager._extractErrorPatterns(buildOutput);
      expect(Array.isArray(patterns)).toBe(true);
      expect(patterns.length).toBeGreaterThan(0);

      const hasModuleError = patterns.some(p => p.includes('Module not found'));
      const hasExportError = patterns.some(p => p.includes('export'));
      expect(hasModuleError).toBe(true);
      expect(hasExportError).toBe(true);
    });
  });

  describe('会话摘要', () => {
    it('应该能够生成会话摘要', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      // 创建一些修复尝试
      const context1 = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      await historyManager.endFileFixAttempt(context1.attemptId, false, null, 'Failed');
      
      const context2 = await historyManager.startFileFixAttempt(filePath, buildOutput, 2);
      await historyManager.endFileFixAttempt(context2.attemptId, true, { success: true }, null);

      const summary = historyManager.generateSessionSummary();

      expect(summary).toHaveProperty('sessionId');
      expect(summary).toHaveProperty('statistics');
      expect(summary).toHaveProperty('strategies');
      expect(summary).toHaveProperty('files');
      expect(summary).toHaveProperty('recommendations');

      expect(summary.statistics.totalFiles).toBe(1);
      expect(summary.statistics.totalAttempts).toBe(2);
      expect(summary.statistics.successfulAttempts).toBe(1);
      expect(summary.statistics.successRate).toBe(0.5);
    });

    it('应该能够保存会话摘要到文件', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      const context = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      await historyManager.endFileFixAttempt(context.attemptId, true, { success: true }, null);

      const summaryPath = await historyManager.saveSessionSummary();
      expect(summaryPath).not.toBeNull();
      expect(await fs.pathExists(summaryPath)).toBe(true);

      const summaryData = await fs.readJson(summaryPath);
      expect(summaryData).toHaveProperty('sessionId');
      expect(summaryData).toHaveProperty('statistics');
    });
  });

  describe('持久化', () => {
    it('应该能够持久化和加载文件历史', async () => {
      const filePath = 'src/test.vue';
      const buildOutput = 'Error: Cannot resolve dependency';
      
      // 创建修复尝试
      const context = await historyManager.startFileFixAttempt(filePath, buildOutput, 1);
      await historyManager.endFileFixAttempt(context.attemptId, true, { success: true }, null);

      // 创建新的历史管理器实例来测试加载
      const newHistoryManager = new FileFixHistoryManager(tempDir, {
        enablePersistence: true,
        verbose: false
      });

      const loadedHistory = await newHistoryManager.getFileHistory(filePath);
      expect(loadedHistory).toHaveLength(1);
      expect(loadedHistory[0].success).toBe(true);
    });
  });
});
