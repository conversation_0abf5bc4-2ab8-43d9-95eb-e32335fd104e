const path = require('path');
const { getMigratorDocPath } = require('../src/frameworks/vue/third-party/MigrationDocReader')

describe('getMigratorDocPath', () => {
  test('should return correct path for scoped package names', () => {
    const docPath = getMigratorDocPath('@riophae/vue-treeselect');
    expect(docPath).toBe(path.resolve(__dirname, '../src/frameworks/vue/third-party/docs/riophae-vue-treeselect.md'));
  });

  test('should handle @tinymce/tinymce-vue correctly', () => {
    const docPath = getMigratorDocPath('@tinymce/tinymce-vue');
    expect(docPath).toBe(path.resolve(__dirname, '../src/frameworks/vue/third-party/docs/tinymce-tinymce-vue.md'));
  });

  test('should handle @wangeditor/editor-for-vue correctly', () => {
    const docPath = getMigratorDocPath('@wangeditor/editor-for-vue');
    expect(docPath).toBe(path.resolve(__dirname, '../src/frameworks/vue/third-party/docs/wangeditor-editor-for-vue.md'));
  });

  test('should return null for non-existent package documentation', () => {
    const docPath = getMigratorDocPath('non-existent-package');
    expect(docPath).toBeNull();
  });

  test('should return null for non-existent scoped package documentation', () => {
    const docPath = getMigratorDocPath('@scope/non-existent-package');
    expect(docPath).toBeNull();
  });
});
