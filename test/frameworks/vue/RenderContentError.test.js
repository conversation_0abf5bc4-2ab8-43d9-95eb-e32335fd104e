const RenderContentTransformer = require('../../../src/frameworks/vue/RenderFunctionTransformer');
const path = require('path');
const fs = require('fs');

describe('RenderContentTransformer - TypeError Issue', () => {
  let transformer;

  beforeEach(() => {
    transformer = new RenderContentTransformer({
      verbose: true
    });
  });

  it('should handle the specific TypeError: Cannot read properties of null (reading \'content\') issue', async () => {
    // 从测试文件中读取包含 renderContent 的 Vue 组件
    const fixturePath = path.resolve(__dirname, '../../fixtures/vue-render-content-error.vue');
    const fixtureContent = fs.readFileSync(fixturePath, 'utf-8');
    
    // 测试转换
    const result = await transformer.transform(fixtureContent, 'vue-render-content-error.vue');
    
    // 验证转换结果
    expect(result).toContain('renderContent({ node, data })');
    expect(result).toContain("import { h } from 'vue'");
    expect(result).not.toContain('renderContent(h,');
    
    // 验证 JSX 转换为 h 函数调用
    expect(result).toMatch(/h\('div',\s*\{\s*"class":\s*"connect_id_title"\s*\},/);
    expect(result).toMatch(/h\('span',\s*\{\s*"class":\s*"connect_id_number",\s*"style":\s*\{"opacity":0\}\s*\},/);
    
    // 验证空格字符被正确保留
    expect(result).toContain('data.title,');
    expect(result).toContain('" ",');
    
    // 验证转换后的代码结构正确
    expect(result).toContain('\"class\": \"connect_id_title\"');
    expect(result).toContain('\"class\": \"connect_id_number\"');
  });
});
