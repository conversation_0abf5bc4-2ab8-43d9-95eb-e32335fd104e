const RenderContentTransformer = require('../../../src/frameworks/vue/RenderFunctionTransformer');
const path = require('path');

describe('RenderContentTransformer', () => {
  let transformer;
  let gogocode;

  beforeEach(() => {
    transformer = new RenderContentTransformer({
      verbose: false,
      aiApiKey: process.env.GLM_API_KEY || process.env.OPENAI_API_KEY,
      aiProvider: 'auto'
    });
    gogocode = require('gogocode');
  });

  describe('hasRenderContent', () => {
    it('should detect renderContent method with Vue 2 signature', () => {
      const code = `
        renderContent(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;
      expect(transformer.hasRenderContent(code)).toBe(true);
    });

    it('should not detect Vue 3 renderContent signature', () => {
      const code = `
        renderContent({ node, data }) {
          return h('div', null, data.title)
        }
      `;
      expect(transformer.hasRenderContent(code)).toBe(false);
    });

    it('should not detect unrelated methods', () => {
      const code = `
        someOtherMethod(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;
      expect(transformer.hasRenderContent(code)).toBe(false);
    });
  });

  describe('transformWithAST', () => {
    it('should transform simple renderContent method', async () => {
      const input = `
        renderContent(h, { node, data }) {
          return (
            <div class="title">
              {data.title}
            </div>
          )
        }
      `;

      const result = await transformer.transformWithAST(input, 'test.vue');

      expect(result.success).toBe(true);
      expect(result.code).toContain('renderContent({ node, data })');
      expect(result.code).toContain("import { h } from 'vue'");
      // 由于现在使用 AST 方式，可能的结果包括 h() 调用或回退到正则方式
      expect(result.code).toMatch(/(h\('div'|<div)/);
    });

    it('should handle complex renderContent with conditions', async () => {
      const input = `
        renderContent(h, { node, data }) {
          if (data.id === '-1') {
            return (
              <div class="custom-node">
                {data.title}
                <span style="opacity:0">{data.id}</span>
              </div>
            )
          } else {
            return (
              <div class="normal-node">
                {data.title}
              </div>
            )
          }
        }
      `;

      const result = await transformer.transformWithAST(input, 'test.vue');

      expect(result.success).toBe(true);
      expect(result.code).toContain('renderContent({ node, data })');
      expect(result.code).toContain("import { h } from 'vue'");
    });

    it('should return original code if no renderContent found', async () => {
      const input = `
        someOtherMethod() {
          return 'hello'
        }
      `;

      const result = await transformer.transformWithAST(input, 'test.vue');

      expect(result.success).toBe(false);
      expect(result.error).toContain('No renderContent methods found');
    });
  });

  describe('ensureHImport', () => {
    it('should add h import if not present', () => {
      const code = `
        <script>
        export default {
          methods: {
            test() {}
          }
        }
        </script>
      `;

      const result = transformer.ensureHImport(code);
      expect(result).toContain("import { h } from 'vue'");
    });

    it('should not duplicate h import if already present', () => {
      const code = `
        <script>
        import { h } from 'vue'
        export default {
          methods: {
            test() {}
          }
        }
        </script>
      `;

      const result = transformer.ensureHImport(code);
      const importMatches = result.match(/import.*h.*from.*vue/g);
      expect(importMatches).toHaveLength(1);
    });

    it('should add h to existing vue imports', () => {
      const code = `
        <script>
        import { ref, reactive } from 'vue'
        export default {
          methods: {
            test() {}
          }
        }
        </script>
      `;

      const result = transformer.ensureHImport(code);
      expect(result).toContain("import { h, ref, reactive } from 'vue'");
    });
  });

  describe('parseInlineStyle', () => {
    it('should parse CSS properties to camelCase object', () => {
      const styleStr = 'opacity:0;margin-top:10px;background-color:red';
      const result = transformer.parseInlineStyle(styleStr);

      expect(result).toEqual({
        opacity: 0,
        marginTop: '10px',
        backgroundColor: 'red'
      });
    });

    it('should handle numeric values', () => {
      const styleStr = 'width:100;height:200px';
      const result = transformer.parseInlineStyle(styleStr);

      expect(result).toEqual({
        width: 100,
        height: '200px'
      });
    });

    it('should handle empty style string', () => {
      const result = transformer.parseInlineStyle('');
      expect(result).toEqual({});
    });
  });

  describe('transform integration', () => {
    it('should transform complete Vue file with renderContent', async () => {
      const input = `
        <template>
          <el-tree :render-content="renderContent" />
        </template>
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="connect_id_title">
                  {data.title}{' '}
                  <span class="connect_id_number" style="opacity:0">
                    {data.id}
                  </span>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");
      // 现在可能使用 AST 方式或正则方式
      expect(result).toMatch(/(h\('div'|<div)/);
      expect(result).not.toContain('renderContent(h,');
    });

    it('should preserve class and style attributes in JSX transformation', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="custom-tree-node">
                  {data.title}
                  <span class="connect_id_number" style="opacity:0">
                    {data.id}
                  </span>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");

      // 检查是否保留了 class 和 style 属性
      if (result.includes("h('div'")) {
        expect(result).toMatch(/class.*custom-tree-node/);
        expect(result).toMatch(/style.*opacity.*0/);
      }
    });

    it('should handle complex JSX with event handlers', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="custom-node">
                  <el-button
                    type="text"
                    size="mini"
                    on-click={() => this.expandAll(false)}
                  >
                    全部折叠
                  </el-button>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");

      // 检查事件处理器转换
      if (result.includes("h('el-button'")) {
        expect(result).toMatch(/(onClick|on-click)/);
      }
    });

    it('should return original code if no renderContent present', async () => {
      const input = `
        <template>
          <div>Hello World</div>
        </template>
        <script>
        export default {
          methods: {
            test() {
              return 'hello'
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');
      expect(result).toBe(input);
    });
  });

  describe('AST support', () => {
    it('should accept AST object for transformation', async () => {
      const input = `
        renderContent(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;

      const gogocode = require('gogocode');
      const ast = gogocode(input);

      const result = await transformer.transform(input, 'test.vue', ast);

      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");
    });

    it('should fall back to regex when AST fails', async () => {
      const input = `
        renderContent(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;

      // 传入无效的 AST
      const invalidAst = null;

      const result = await transformer.transform(input, 'test.vue', invalidAst);

      expect(result).toContain('renderContent({ node, data })');
    });
  });

  describe('statistics', () => {
    it('should track transformation statistics', async () => {
      const input = `
        renderContent(h, { node, data }) {
          return <div>{data.title}</div>
        }
      `;

      await transformer.transform(input, 'test1.vue');
      await transformer.transform(input, 'test2.vue');
      await transformer.transform('no renderContent here', 'test3.vue');

      const stats = transformer.getStats();
      expect(stats.total).toBe(3);
      // 由于现在有 AST 和正则两种方式，可能会成功
      expect(stats.astSuccess + stats.aiSuccess).toBeGreaterThanOrEqual(2);
      expect(stats.failed).toBe(0);
      expect(stats.skipped).toBe(0);
    });
  });

  describe('h function formatting', () => {
    it('should format simple h calls appropriately', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return <div class="simple">{data.title}</div>
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'simple.vue');

      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");

      // 验证转换发生了（无论是 AST 还是 AI 转换）
      if (result.includes("h('div'")) {
        // AST 转换：验证格式化
        expect(result).toMatch(/h\('div',[\s\S]*"class":\s*"simple"/);
      } else {
        // AI 转换：验证基本结构
        expect(result).not.toContain('renderContent(h,');
      }
    });

    it('should format complex h calls with proper indentation', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="custom-node" style="color: red;">
                  {data.title}
                  <span class="id-span">{data.id}</span>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'complex.vue');

      expect(result).toContain('renderContent({ node, data })');

      // 验证多行格式 - 复杂元素应该使用多行
      expect(result).toMatch(/h\('div',[\s\S]*\{[\s\S]*"class":/);
      expect(result).toMatch(/\[[\s\S]*data\.title,/);

      // 验证嵌套的 span 元素
      expect(result).toMatch(/h\('span',[\s\S]*"class":\s*"id-span"/);
    });

    it('should format nested h calls with consistent indentation', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="wrapper">
                  <span class="title">{data.title}</span>
                  <div class="buttons">
                    <el-button type="primary" on-click={() => this.edit()}>编辑</el-button>
                    <el-button type="danger" on-click={() => this.delete()}>删除</el-button>
                  </div>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'nested.vue');

      expect(result).toContain('renderContent({ node, data })');

      // 验证嵌套结构的格式化
      expect(result).toMatch(/h\('div',[\s\S]*\[\s*\n[\s\S]*h\('span'/);
      expect(result).toMatch(/h\('div',[\s\S]*h\('el-button'/);

      // 验证事件处理器格式
      expect(result).toContain('"onClick": () => this.edit()');
      expect(result).toContain('"onClick": () => this.delete()');
    });

    it('should handle mixed simple and complex elements appropriately', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div>
                  <i class="icon"></i>
                  {data.title}
                  <el-button type="text" size="mini" on-click={() => this.action()}>
                    操作
                  </el-button>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'mixed.vue');

      expect(result).toContain('renderContent({ node, data })');

      // 简单的 i 标签应该相对简洁
      expect(result).toMatch(/h\('i',\s*\{\s*"class":\s*"icon"\s*\}/);

      // 复杂的 el-button 应该使用多行格式
      expect(result).toMatch(/h\('el-button',[\s\S]*"onClick":/);
    });

    it('should preserve proper spacing in formatted output', async () => {
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div class="node">
                  {data.title}{' '}
                  <span style="opacity:0">{data.id}</span>
                </div>
              )
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'spacing.vue');

      expect(result).toContain('renderContent({ node, data })');

      // 验证空格字符被正确保留
      expect(result).toContain('data.title,');
      expect(result).toContain('" ",');

      // 验证 style 对象格式
      expect(result).toContain('"style": {"opacity":0}');
    });
  });

  describe('real project scenarios', () => {
    it('should handle complex real-world renderContent with conditional JSX', async () => {
      // 基于真实项目中的 renderContent 方法
      const input = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              // 添加并隐藏id  以便获取DOM时能获取到对应id
              if (data.id === '-1') {
                return (
                  <div class="custom-tree-node">
                    {data.title}{' '}
                    <span class="connect_id_number" style="opacity:0">
                      {data.id}
                    </span>
                    <span>
                      <el-button
                        type="text"
                        size="mini"
                        on-click={() => this.expandAll(false)}
                      >
                        全部折叠
                      </el-button>
                      <el-button
                        type="text"
                        size="mini"
                        on-click={() => this.expandAll(true)}
                      >
                        全部展开
                      </el-button>
                    </span>
                  </div>
                )
              } else {
                return (
                  <div class="connect_id_title">
                    {data.title}{' '}
                    <span class="connect_id_number" style="opacity:0">
                      {data.id}
                    </span>
                  </div>
                )
              }
            }
          }
        }
        </script>
      `;

      const result = await transformer.transform(input, 'real-project.vue');

      expect(result).toContain('renderContent({ node, data })');
      expect(result).toContain("import { h } from 'vue'");
      expect(result).not.toContain('renderContent(h,');

      // 验证条件逻辑保持不变
      expect(result).toContain("if (data.id === '-1')");
      expect(result).toContain('} else {');

      // 验证注释保持不变
      expect(result).toContain('// 添加并隐藏id  以便获取DOM时能获取到对应id');

      // 验证格式化效果
      expect(result).toMatch(/h\('div',[\s\S]*\{[\s\S]*"class":/);
      expect(result).toMatch(/\[\s*\n[\s\S]*data\.title,/);
    });

    it('should not break when encountering null content errors', async () => {
      // 测试可能导致 "Cannot read properties of null (reading 'content')" 错误的场景
      const problematicInput = `
        <script>
        export default {
          methods: {
            renderContent(h, { node, data }) {
              return (
                <div>
                  {data && data.title ? data.title : ''}
                  <span>{data?.id || ''}</span>
                </div>
              )
            }
          }
        }
        </script>
      `;

      // 这个测试应该不会抛出异常
      expect(async () => {
        await transformer.transform(problematicInput, 'problematic.vue');
      }).not.toThrow();
    });
  });
});
