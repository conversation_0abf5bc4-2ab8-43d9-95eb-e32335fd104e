const fs = require('fs-extra');
const path = require('path');
const AutoLoginManager = require('../src/domain/runtime-validation/AutoLoginManager');

describe('AutoLoginManager', () => {
  let autoLoginManager;
  let mockPage;
  const testConfigPath = path.join(__dirname, '.test-login-config.json');

  beforeEach(() => {
    // 创建模拟的 page 对象
    mockPage = {
      url: () => 'http://localhost:3000/#/login',
      waitForSelector: jest.fn().mockResolvedValue(true),
      $: jest.fn(),
      click: jest.fn(),
      type: jest.fn(),
      evaluate: jest.fn(),
      goto: jest.fn().mockResolvedValue({ ok: () => true }),
      waitForFunction: jest.fn().mockResolvedValue(true)
    };

    // 创建 AutoLoginManager 实例
    autoLoginManager = new AutoLoginManager({
      username: 'testuser',
      password: 'testpass',
      verbose: true,
      aiEnabled: false, // 测试时禁用 AI
      configPath: testConfigPath
    });
  });

  afterEach(async () => {
    // 清理测试配置文件
    if (await fs.pathExists(testConfigPath)) {
      await fs.remove(testConfigPath);
    }
  });

  describe('配置文件管理', () => {
    test('应该能够保存和加载登录配置', async () => {
      const testConfig = {
        timestamp: new Date().toISOString(),
        username: 'testuser',
        steps: [
          { action: 'wait', selector: 'input[name="username"]', timeout: 5000 },
          { action: 'type', selector: 'input[name="username"]', value: 'testuser' }
        ],
        success: true
      };

      // 保存配置
      const saveResult = await autoLoginManager.saveLoginConfig(testConfig);
      expect(saveResult).toBe(true);

      // 加载配置
      const loadResult = await autoLoginManager.loadLoginConfig();
      expect(loadResult).toBe(true);
      expect(autoLoginManager.loginConfig).toEqual(testConfig);
    });

    test('加载不存在的配置文件应该返回 false', async () => {
      const result = await autoLoginManager.loadLoginConfig();
      expect(result).toBe(false);
    });
  });

  describe('默认登录逻辑', () => {
    test('找不到登录表单时应该返回 false', async () => {
      mockPage.waitForSelector.mockRejectedValue(new Error('Selector not found'));

      const result = await autoLoginManager.tryDefaultLogin(mockPage);
      expect(result).toBe(false);
    });
  });

  describe('登录步骤执行', () => {
    test('应该能够执行 wait 步骤', async () => {
      const step = { action: 'wait', selector: '.login-form', timeout: 5000 };

      await autoLoginManager.executeLoginStep(mockPage, step);

      expect(mockPage.waitForSelector).toHaveBeenCalledWith('.login-form', {
        timeout: 5000,
        visible: true
      });
    });

    test('应该能够执行 click 步骤', async () => {
      const step = { action: 'click', selector: '.login-button' };

      await autoLoginManager.executeLoginStep(mockPage, step);

      expect(mockPage.click).toHaveBeenCalledWith('.login-button');
    });

    test('应该能够执行 type 步骤', async () => {
      const step = { action: 'type', selector: 'input[name="username"]', value: 'testuser' };

      await autoLoginManager.executeLoginStep(mockPage, step);

      expect(mockPage.click).toHaveBeenCalledWith('input[name="username"]', { clickCount: 3 });
      expect(mockPage.type).toHaveBeenCalledWith('input[name="username"]', 'testuser', { delay: 100 });
    });

    test('未知步骤类型应该被忽略', async () => {
      const step = { action: 'unknown', selector: '.test' };

      // 不应该抛出错误
      await expect(autoLoginManager.executeLoginStep(mockPage, step)).resolves.toBeUndefined();
    });
  });

  describe('DOM 结构获取', () => {
    test('应该能够获取登录页面的 DOM 结构', async () => {
      const mockDOMStructure = {
        forms: [{ tagName: 'FORM', className: 'login-form', id: 'loginForm' }],
        inputs: [
          { type: 'text', name: 'username', id: 'username', className: 'form-input' },
          { type: 'password', name: 'password', id: 'password', className: 'form-input' }
        ],
        buttons: [{ tagName: 'BUTTON', type: 'submit', className: 'login-btn', textContent: '登录' }],
        url: 'http://localhost:3000/#/login',
        title: '用户登录'
      };

      mockPage.evaluate.mockResolvedValue(mockDOMStructure);

      const result = await autoLoginManager.getLoginPageDOM(mockPage);

      expect(result).toEqual(mockDOMStructure);
      expect(mockPage.evaluate).toHaveBeenCalled();
    });

    test('获取 DOM 结构失败时应该返回 null', async () => {
      mockPage.evaluate.mockRejectedValue(new Error('Evaluation failed'));

      const result = await autoLoginManager.getLoginPageDOM(mockPage);

      expect(result).toBeNull();
    });
  });

  describe('AI 响应解析', () => {
    test('应该能够解析包含 JSON 代码块的响应', () => {
      const response = `
这是一个登录步骤的分析结果：

\`\`\`json
[
  {
    "action": "wait",
    "selector": "input[name='username']",
    "timeout": 5000
  },
  {
    "action": "type",
    "selector": "input[name='username']",
    "value": "testuser"
  }
]
\`\`\`

以上是推荐的登录步骤。
      `;

      const result = autoLoginManager.parseLoginStepsFromResponse(response);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        action: 'wait',
        selector: "input[name='username']",
        timeout: 5000
      });
    });

    test('应该能够解析纯 JSON 响应', () => {
      const response = JSON.stringify([
        { action: 'click', selector: '.login-btn' }
      ]);

      const result = autoLoginManager.parseLoginStepsFromResponse(response);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({ action: 'click', selector: '.login-btn' });
    });

    test('解析无效响应时应该返回空数组', () => {
      const response = 'This is not a valid JSON response';

      const result = autoLoginManager.parseLoginStepsFromResponse(response);

      expect(result).toEqual([]);
    });
  });
});
