const fs = require('fs-extra');
const path = require('path');
const PackageJsonMerger = require('../src/features/package-json/PackageJsonMerger');

describe('PackageJsonMerger', () => {
  let tempDir;
  let sourceDir;
  let targetDir;
  let merger;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = path.join(__dirname, 'temp', 'package-json-merger');
    sourceDir = path.join(tempDir, 'source');
    targetDir = path.join(tempDir, 'target');

    await fs.ensureDir(sourceDir);
    await fs.ensureDir(targetDir);

    // 创建测试用的 package.json 文件
    await createTestPackageJsonFiles();

    merger = new PackageJsonMerger(sourceDir, targetDir, {
      verbose: false,
      dryRun: true
    });
  });

  afterEach(async () => {
    // 清理临时文件
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  async function createTestPackageJsonFiles() {
    // 源项目 package.json (Vue 2 项目)
    const sourcePackageJson = {
      name: 'vue2-source-project',
      version: '1.0.0',
      description: 'Vue 2 source project',
      dependencies: {
        vue: '^2.6.14',
        'vue-router': '^3.5.4',
        axios: '^0.27.2',
        lodash: '^4.17.21'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14',
        '@vue/cli-service': '^4.5.15'
      },
      scripts: {
        serve: 'vue-cli-service serve',
        build: 'vue-cli-service build',
        test: 'npm run test:unit'
      }
    };

    // 目标项目 package.json (Vue 3 项目模板)
    const targetPackageJson = {
      name: 'vue3-target-project',
      version: '2.0.0',
      description: 'Vue 3 target project',
      dependencies: {
        vue: '^3.4.0',
        'vue-router': '^4.5.0',
        'element-plus': '^2.9.0'
      },
      devDependencies: {
        '@vue/cli-service': '^5.0.8',
        vite: '^4.5.0'
      },
      scripts: {
        serve: 'vue-cli-service serve',
        build: 'vue-cli-service build',
        dev: 'vite'
      }
    };

    await fs.writeJson(path.join(sourceDir, 'package.json'), sourcePackageJson, { spaces: 2 });
    await fs.writeJson(path.join(targetDir, 'package.json'), targetPackageJson, { spaces: 2 });
  }

  describe('基本功能测试', () => {
    it('应该能够读取源项目和目标项目的 package.json', async () => {
      const sourcePackageJson = await merger.readPackageJson(sourceDir);
      const targetPackageJson = await merger.readPackageJson(targetDir);

      expect(sourcePackageJson).not.toBeNull();
      expect(targetPackageJson).not.toBeNull();
      expect(sourcePackageJson.name).toBe('vue2-source-project');
      expect(targetPackageJson.name).toBe('vue3-target-project');
    });

    it('应该能够执行合并操作', async () => {
      const result = await merger.merge();

      expect(result.success).toBe(true);
      expect(result.mergedPackageJson).not.toBeNull();
      expect(Array.isArray(result.changes)).toBe(true);
    });
  });

  describe('依赖合并测试', () => {
    it('应该保留目标项目的 Vue 3 依赖', async () => {
      const result = await merger.merge();
      const merged = result.mergedPackageJson;

      expect(merged.dependencies.vue).toBe('^3.4.0');
      expect(merged.dependencies['vue-router']).toBe('^4.5.0');
    });

    it('应该跳过 Vue 2 官方依赖', async () => {
      const result = await merger.merge();
      const merged = result.mergedPackageJson;

      expect(merged.devDependencies).not.toHaveProperty('vue-template-compiler');
    });

    it('应该添加兼容的第三方依赖', async () => {
      const result = await merger.merge();
      const merged = result.mergedPackageJson;

      // axios 和 lodash 应该被添加，因为它们是兼容的
      expect(merged.dependencies).toHaveProperty('axios');
      expect(merged.dependencies).toHaveProperty('lodash');
    });

    it('应该处理第三方组件映射', async () => {
      const result = await merger.merge();

      // 这个测试依赖于 ComponentDependencyMapper 的配置
      expect(result.changes.length).toBeGreaterThan(0);
    });
  });

  describe('错误处理测试', () => {
    it('应该处理源项目 package.json 不存在的情况', async () => {
      await fs.remove(path.join(sourceDir, 'package.json'));

      await expect(merger.merge()).rejects.toThrow('源项目中未找到 package.json');
    });

    it('应该处理目标项目 package.json 不存在的情况', async () => {
      await fs.remove(path.join(targetDir, 'package.json'));

      await expect(merger.merge()).rejects.toThrow('目标项目中未找到 package.json');
    });

    it('应该处理无效的 JSON 文件', async () => {
      await fs.writeFile(path.join(sourceDir, 'package.json'), '{ invalid json }');

      const sourcePackageJson = await merger.readPackageJson(sourceDir);
      expect(sourcePackageJson).toBeNull();
    });
  });
});
