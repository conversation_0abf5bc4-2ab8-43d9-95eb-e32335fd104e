# Vue Migrator 集成测试

本目录包含 Vue Migrator 的端到端集成测试，用于验证完整的迁移流程。

## 测试文件

### 1. `migrator-basic.test.js`
基本功能测试，验证：
- 迁移命令能够成功执行
- 帮助信息正确显示
- 错误处理正常工作

### 2. `vue-migrator-integration.test.js`
完整集成测试，验证：
- 完整的迁移流程
- 迁移报告生成
- 项目结构保持完整
- package.json 正确更新
- 各个迁移阶段正常完成

## 运行测试

### 运行所有 E2E 测试
```bash
npm run test:e2e
```

### 运行基本测试
```bash
npm run test:migrator-basic
```

### 运行完整集成测试
```bash
npm run test:migrator
```

### 运行简单测试脚本
```bash
npm run test:migrator-simple
```

## 测试环境要求

1. **测试项目**: 确保 `test-project` 目录存在且包含有效的 Vue 2 项目
2. **依赖**: 所有项目依赖已安装 (`npm install`)
3. **权限**: 有读写文件系统的权限
4. **时间**: 每个测试可能需要 1-3 分钟完成

## 测试流程

1. **准备阶段**: 检查测试项目存在，清理之前的测试结果
2. **复制阶段**: 预先创建目标目录并复制必要文件（解决复制逻辑问题）
3. **迁移阶段**: 执行完整的 Vue 2 到 Vue 3 迁移
4. **验证阶段**: 检查迁移结果的正确性

## 验证内容

### 文件结构验证
- ✅ 基本文件存在 (`package.json`, `src/main.js`, `src/App.vue`)
- ✅ 目录结构保持完整
- ✅ 迁移报告生成
- ✅ 迁移上下文保存

### 依赖验证
- ✅ Vue 升级到 3.x
- ✅ Vue Router 升级到 4.x
- ✅ Vuex 升级到 4.x
- ✅ Element Plus 替换 Element UI

### 功能验证
- ✅ 命令行参数处理
- ✅ 错误处理
- ✅ 日志输出
- ✅ 迁移统计

## 故障排除

### 常见问题

1. **package.json 未找到错误**
   - 原因: 复制逻辑问题
   - 解决: 测试中预先复制 package.json 文件

2. **超时错误**
   - 原因: 迁移过程较长
   - 解决: 增加测试超时时间到 3 分钟

3. **AI 服务错误**
   - 原因: AI API 调用失败
   - 解决: 使用 `--skip-ai` 参数跳过 AI 修复

4. **构建错误**
   - 原因: 构建过程复杂
   - 解决: 使用 `--skip-build` 参数跳过构建

### 调试技巧

1. **查看详细输出**: 使用 `--verbose` 参数
2. **检查日志**: 查看 `migration-logs` 目录
3. **分析报告**: 查看生成的迁移报告
4. **手动运行**: 直接运行 `node bin/vue-migrator.js` 命令

## 测试数据

测试使用 `test-project` 作为源项目，这是一个包含常见 Vue 2 特性的示例项目：
- Vue 2.x 语法
- Vue Router 3.x
- Vuex 3.x
- Element UI
- 各种 Vue 生态插件

迁移后的项目位于 `test-project-migrated`，包含：
- Vue 3.x 语法
- Vue Router 4.x
- Vuex 4.x
- Element Plus
- 兼容的 Vue 3 插件

## 贡献指南

添加新测试时请：
1. 遵循现有的测试模式
2. 添加适当的超时设置
3. 包含清理逻辑
4. 更新此 README 文档
