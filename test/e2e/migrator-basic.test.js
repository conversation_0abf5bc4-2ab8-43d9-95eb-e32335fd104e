const path = require('path');
const fs = require('fs-extra');
const { spawn } = require('child_process');

describe('Vue Migrator Basic Tests', () => {
  const testProjectPath = path.join(__dirname, '../../test-project');
  const testProjectMigratedPath = path.join(__dirname, '../../test-project-migrated');
  const vueMigratorBin = path.join(__dirname, '../../bin/vue-migrator.js');

  beforeAll(async () => {
    // 确保测试项目存在
    const exists = await fs.pathExists(testProjectPath);
    if (!exists) {
      throw new Error(`Test project not found at ${testProjectPath}`);
    }
  });

  afterEach(async () => {
    // 清理迁移后的项目
    if (await fs.pathExists(testProjectMigratedPath)) {
      await fs.remove(testProjectMigratedPath);
    }
  });

  test('should show help when no arguments provided', async () => {
    const result = await runCommand('node', [vueMigratorBin]);

    // 验证显示帮助信息（帮助信息输出到 stderr）
    const output = result.stdout + result.stderr;
    expect(output).toContain('Vue 2 到 Vue 3 统一迁移工具');
    expect(output).toContain('Usage:');
  }, 30000);

  test('should handle invalid source path', async () => {
    const invalidPath = path.join(__dirname, 'non-existent-project');

    const result = await runCommand('node', [
      vueMigratorBin,
      'auto',
      invalidPath,
      testProjectMigratedPath
    ]);

    // 验证命令失败并显示错误信息
    expect(result.exitCode).toBe(1);
    expect(result.stderr).toContain('源项目路径不存在');
  }, 30000);
});

/**
 * 运行命令并返回结果
 * @param {string} command - 命令
 * @param {string[]} args - 参数
 * @returns {Promise<{exitCode: number, stdout: string, stderr: string}>}
 */
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command} ${args.join(' ')}`);

    const child = spawn(command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      const text = data.toString();
      stdout += text;
      // 实时输出，便于调试
      process.stdout.write(`[STDOUT] ${text}`);
    });

    child.stderr.on('data', (data) => {
      const text = data.toString();
      stderr += text;
      // 实时输出，便于调试
      process.stderr.write(`[STDERR] ${text}`);
    });

    child.on('close', (code) => {
      console.log(`Command finished with exit code: ${code}`);
      resolve({
        exitCode: code,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      console.error('Command error:', error);
      reject(error);
    });

    // 设置超时
    const timeout = setTimeout(() => {
      console.log('Command timeout, killing process...');
      child.kill('SIGTERM');
      reject(new Error('Command timeout'));
    }, 200000); // 200秒超时

    child.on('close', () => {
      clearTimeout(timeout);
    });
  });
}
