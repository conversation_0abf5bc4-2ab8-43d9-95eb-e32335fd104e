const path = require('path');
const fs = require('fs-extra');
const { spawn } = require('child_process');

describe('Vue Migrator Integration Tests', () => {
  const testProjectPath = path.join(__dirname, '../../test-project');
  const testProjectMigratedPath = path.join(__dirname, '../../test-project-migrated');

  beforeAll(async () => {
    // 确保测试项目存在
    const exists = await fs.pathExists(testProjectPath);
    if (!exists) {
      throw new Error(`Test project not found at ${testProjectPath}`);
    }
  });

  afterEach(async () => {
    // 清理迁移后的项目
    if (await fs.pathExists(testProjectMigratedPath)) {
      await fs.remove(testProjectMigratedPath);
    }
  });

  describe('Auto Migration Command', () => {
    test('should validate source project exists', async () => {
      const nonExistentPath = path.join(__dirname, 'non-existent-project');

      // 执行迁移命令，使用不存在的源项目
      const result = await runVueMigrator(['auto', nonExistentPath, testProjectMigratedPath]);

      // 验证命令失败
      expect(result.exitCode).toBe(1);
      expect(result.stderr).toContain('源项目路径不存在');
    }, 30000);
  });

});

/**
 * 运行 vue-migrator 命令
 * @param {string[]} args - 命令参数
 * @returns {Promise<{exitCode: number, stdout: string, stderr: string}>}
 */
function runVueMigrator(args) {
  const vueMigratorBinPath = path.join(__dirname, '../../bin/vue-migrator.js');
  return new Promise((resolve, reject) => {
    const child = spawn('node', [vueMigratorBinPath, ...args], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        exitCode: code,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      reject(error);
    });

    // 设置超时
    setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error('Command timeout'));
    }, 180000); // 3分钟超时
  });
}
