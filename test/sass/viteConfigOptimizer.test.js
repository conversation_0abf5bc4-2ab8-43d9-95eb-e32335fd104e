const path = require('path');
const fs = require('fs-extra');
const ViteConfigOptimizer = require('../../src/features/sass/viteConfigOptimizer');

describe('ViteConfigOptimizer', () => {
  let tempDir;
  let optimizer;

  beforeEach(async () => {
    tempDir = await global.testUtils.createTempDir('vite-optimizer-');
    optimizer = new ViteConfigOptimizer(tempDir, { verbose: false });
  });

  describe('初始化', () => {
    test('应该正确初始化 Vite 配置优化器', () => {
      expect(optimizer.projectPath).toBe(tempDir);
      expect(optimizer.configPath).toBe(path.join(tempDir, 'vite.config.js'));
      expect(optimizer.optimizations).toEqual([]);
    });
  });

  describe('配置文件检查', () => {
    test('应该检测配置文件是否存在', async () => {
      expect(await optimizer.configExists()).toBe(false);

      await fs.writeFile(optimizer.configPath, 'export default {};');
      expect(await optimizer.configExists()).toBe(true);
    });

    test('应该创建默认配置文件', async () => {
      await optimizer.createDefaultConfig();

      expect(await optimizer.configExists()).toBe(true);

      const content = await fs.readFile(optimizer.configPath, 'utf8');
      expect(content).toContain('import { defineConfig } from \'vite\'');
      expect(content).toContain('loadPaths');
      expect(content).toContain('node_modules');
    });
  });

  describe('配置文件解析', () => {
    test('应该解析基本的 Vite 配置', () => {
      const content = `
        import { defineConfig } from 'vite';
        import vue from '@vitejs/plugin-vue';
        
        export default defineConfig({
          plugins: [vue()],
          resolve: {
            alias: {
              '@': path.resolve(__dirname, 'src')
            }
          }
        });
      `;

      const config = optimizer.parseConfig(content);

      expect(config.hasVuePlugin).toBe(true);
      expect(config.hasPathAlias).toBe(true);
      expect(config.hasSassConfig).toBe(false);
    });

    test('应该检测 Sass 配置', () => {
      const content = `
        export default {
          css: {
            preprocessorOptions: {
              scss: {
                loadPaths: ['node_modules']
              }
            }
          }
        };
      `;

      const config = optimizer.parseConfig(content);

      expect(config.hasSassConfig).toBe(true);
      expect(config.hasLoadPaths).toBe(true);
    });

    test('应该检测 additionalData', () => {
      const content = `
        export default {
          css: {
            preprocessorOptions: {
              scss: {
                additionalData: '@use "src/styles" as *;'
              }
            }
          }
        };
      `;

      const config = optimizer.parseConfig(content);

      expect(config.hasAdditionalData).toBe(true);
    });
  });

  describe('优化建议生成', () => {
    test('应该为缺少 loadPaths 的配置生成建议', async () => {
      const content = `export default { plugins: [] };`;
      await fs.writeFile(optimizer.configPath, content);

      await optimizer.analyzeCurrentConfig();
      await optimizer.generateOptimizations();

      const loadPathsOpt = optimizer.optimizations.find(opt => opt.id === 'add-load-paths');
      expect(loadPathsOpt).toBeDefined();
      expect(loadPathsOpt.priority).toBe('high');
      expect(loadPathsOpt.required).toBe(true);
    });

    test('应该为缺少路径别名的配置生成建议', async () => {
      const content = `export default { plugins: [] };`;
      await fs.writeFile(optimizer.configPath, content);

      await optimizer.analyzeCurrentConfig();
      await optimizer.generateOptimizations();

      const aliasOpt = optimizer.optimizations.find(opt => opt.id === 'add-path-alias');
      expect(aliasOpt).toBeDefined();
      expect(aliasOpt.priority).toBe('medium');
    });

    test('应该为缺少 Vue 插件的配置生成建议', async () => {
      const content = `export default { plugins: [] };`;
      await fs.writeFile(optimizer.configPath, content);

      await optimizer.analyzeCurrentConfig();
      await optimizer.generateOptimizations();

      const vueOpt = optimizer.optimizations.find(opt => opt.id === 'add-vue-plugin');
      expect(vueOpt).toBeDefined();
      expect(vueOpt.priority).toBe('high');
    });

    test('应该为使用 additionalData 的配置生成警告', async () => {
      const content = `
        export default {
          css: {
            preprocessorOptions: {
              scss: {
                additionalData: '@use "src/styles" as *;'
              }
            }
          }
        };
      `;
      await fs.writeFile(optimizer.configPath, content);

      await optimizer.analyzeCurrentConfig();
      await optimizer.generateOptimizations();

      const additionalDataOpt = optimizer.optimizations.find(opt => opt.id === 'review-additional-data');
      expect(additionalDataOpt).toBeDefined();
      expect(additionalDataOpt.priority).toBe('low');
    });
  });

  describe('Element Plus 配置检测', () => {
    test('应该检测 Element Plus 依赖', async () => {
      const packageJson = {
        dependencies: {
          'element-plus': '^2.0.0'
        }
      };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      const needsConfig = await optimizer.needsElementPlusConfig();
      expect(needsConfig).toBe(true);
    });

    test('应该处理没有 Element Plus 的项目', async () => {
      const packageJson = {
        dependencies: {
          vue: '^3.0.0'
        }
      };
      await fs.writeJson(path.join(tempDir, 'package.json'), packageJson);

      const needsConfig = await optimizer.needsElementPlusConfig();
      expect(needsConfig).toBe(false);
    });
  });

  describe('配置优化应用', () => {
    test('应该添加 loadPaths 配置', () => {
      const content = `export default defineConfig({
  plugins: [vue()]
});`;

      const result = optimizer.addLoadPaths(content);

      expect(result).toContain('css:');
      expect(result).toContain('preprocessorOptions:');
      expect(result).toContain('scss:');
      expect(result).toContain('loadPaths');
      expect(result).toContain('node_modules');
    });

    test('应该在现有 css 配置中添加 loadPaths', () => {
      const content = `export default defineConfig({
  css: {
    devSourcemap: true
  }
});`;

      const result = optimizer.addLoadPaths(content);

      expect(result).toContain('preprocessorOptions:');
      expect(result).toContain('loadPaths');
    });

    test('应该在现有 scss 配置中添加 loadPaths', () => {
      const content = `export default defineConfig({
  css: {
    preprocessorOptions: {
      scss: {
        charset: false
      }
    }
  }
});`;

      const result = optimizer.addLoadPaths(content);

      expect(result).toContain('loadPaths: [path.resolve(__dirname, \'node_modules\')],');
    });

    test('应该添加路径别名配置', () => {
      const content = `export default defineConfig({
  plugins: [vue()]
});`;

      const result = optimizer.addPathAlias(content);

      expect(result).toContain('resolve:');
      expect(result).toContain('alias:');
      expect(result).toContain('\'@\': path.resolve(__dirname, \'src\')');
    });

    test('应该添加 Vue 插件', () => {
      const content = `import { defineConfig } from 'vite';

export default defineConfig({
  plugins: []
});`;

      const result = optimizer.addVuePlugin(content);

      expect(result).toContain('import vue from \'@vitejs/plugin-vue\';');
      expect(result).toContain('plugins: [vue(),');
    });
  });

  describe('配置验证', () => {
    test('应该验证有效的配置', async () => {
      const validContent = `
        import { defineConfig } from 'vite';
        import vue from '@vitejs/plugin-vue';
        import path from 'path';
        
        export default defineConfig({
          plugins: [vue()],
          css: {
            preprocessorOptions: {
              scss: {
                loadPaths: [path.resolve(__dirname, 'node_modules')]
              }
            }
          }
        });
      `;

      await fs.writeFile(optimizer.configPath, validContent);
      await optimizer.analyzeCurrentConfig();

      const validation = await optimizer.validateConfig();

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toEqual([]);
    });

    test('应该检测缺少必需配置的问题', async () => {
      const invalidContent = `export default { plugins: [] };`;

      await fs.writeFile(optimizer.configPath, invalidContent);
      await optimizer.analyzeCurrentConfig();

      const validation = await optimizer.validateConfig();

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('缺少 Sass loadPaths 配置');
    });

    test('应该检测语法错误', async () => {
      const syntaxErrorContent = `export default {
        plugins: [
        // 缺少闭合括号
      `;

      await fs.writeFile(optimizer.configPath, syntaxErrorContent);

      const validation = await optimizer.validateConfig();

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => error.includes('语法错误'))).toBe(true);
    });
  });

  describe('JavaScript 语法检查', () => {
    test('应该验证有效的 JavaScript', () => {
      const validContent = `
        export default {
          plugins: [],
          resolve: {
            alias: {}
          }
        };
      `;

      expect(optimizer.isValidJavaScript(validContent)).toBe(true);
    });

    test('应该检测括号不匹配', () => {
      const invalidContent = `
        export default {
          plugins: [
          // 缺少闭合括号
        };
      `;

      expect(optimizer.isValidJavaScript(invalidContent)).toBe(false);
    });

    test('应该检测圆括号不匹配', () => {
      const invalidContent = `
        export default defineConfig({
          plugins: [vue()]
          // 缺少 defineConfig 的闭合圆括号
        };
      `;

      expect(optimizer.isValidJavaScript(invalidContent)).toBe(false);
    });
  });

  describe('完整优化流程', () => {
    test('应该执行完整的优化流程', async () => {
      // 创建一个基本的配置文件
      const basicContent = `export default { plugins: [] };`;
      await fs.writeFile(optimizer.configPath, basicContent);

      const result = await optimizer.optimize();

      expect(result.optimizations.length).toBeGreaterThan(0);
      expect(result.validation).toBeDefined();

      // 检查配置文件是否被更新
      const updatedContent = await fs.readFile(optimizer.configPath, 'utf8');
      expect(updatedContent).toContain('loadPaths');
    });

    test('应该处理不存在配置文件的情况', async () => {
      const result = await optimizer.optimize();

      expect(await optimizer.configExists()).toBe(true);
      expect(result.optimizations.length).toBeGreaterThan(0);
    });

    test('应该备份原配置文件', async () => {
      const originalContent = `export default { plugins: [] };`;
      await fs.writeFile(optimizer.configPath, originalContent);

      optimizer.options.backup = true;
      await optimizer.optimize();

      const backupPath = `${optimizer.configPath}.backup`;
      expect(await fs.pathExists(backupPath)).toBe(true);

      const backupContent = await fs.readFile(backupPath, 'utf8');
      expect(backupContent).toBe(originalContent);
    });
  });

  describe('配置报告生成', () => {
    test('应该生成配置报告', () => {
      optimizer.optimizations = [
        { id: 'test-opt', title: 'Test Optimization', description: 'Test description' }
      ];

      const report = optimizer.generateConfigReport();

      expect(report.configFile).toBe('vite.config.js');
      expect(report.optimizations).toEqual(optimizer.optimizations);
      expect(report.recommendations).toBeDefined();
      expect(report.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('优化结果打印', () => {
    test('应该打印优化结果', () => {
      const result = {
        optimizations: [
          { title: 'Test Optimization', description: 'Test description', required: true }
        ],
        validation: {
          isValid: true,
          errors: [],
          warnings: ['Test warning'],
          suggestions: ['Test suggestion']
        }
      };

      // 这个测试主要确保打印函数不会抛出错误
      expect(() => optimizer.printOptimizationResults(result)).not.toThrow();
    });

    test('应该打印验证失败的结果', () => {
      const result = {
        optimizations: [],
        validation: {
          isValid: false,
          errors: ['Test error'],
          warnings: [],
          suggestions: []
        }
      };

      expect(() => optimizer.printOptimizationResults(result)).not.toThrow();
    });
  });

  describe('错误处理', () => {
    test('应该处理配置文件读取错误', async () => {
      // 模拟文件读取错误
      jest.spyOn(fs, 'readFile').mockRejectedValueOnce(new Error('Permission denied'));

      await expect(optimizer.analyzeCurrentConfig()).resolves.not.toThrow();
    });

    test('应该处理 package.json 读取错误', async () => {
      // 模拟 package.json 读取错误
      jest.spyOn(fs, 'pathExists').mockResolvedValueOnce(false);

      const needsConfig = await optimizer.needsElementPlusConfig();
      expect(needsConfig).toBe(false);
    });
  });
});
