const { expect } = require('chai');
const sinon = require('sinon');
const { 
  PuppeteerAdapter, 
  PuppeteerBrowserAdapter, 
  PuppeteerPageAdapter,
  PuppeteerElementAdapter,
  PuppeteerResponseAdapter
} = require('../../../src/infrastructure/browser/PuppeteerAdapter');

describe('PuppeteerAdapter', () => {
  let adapter;
  let mockPuppeteer;

  beforeEach(() => {
    adapter = new PuppeteerAdapter();
    
    // Mock puppeteer module
    mockPuppeteer = {
      launch: sinon.stub(),
      createBrowserFetcher: sinon.stub(),
      PUPPETEER_REVISIONS: { chromium: 'test-revision' }
    };
    
    adapter.puppeteer = mockPuppeteer;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('launch', () => {
    it('should launch browser with correct options', async () => {
      const mockBrowser = { test: 'browser' };
      mockPuppeteer.launch.resolves(mockBrowser);

      const options = {
        headless: true,
        args: ['--no-sandbox'],
        executablePath: '/path/to/chrome'
      };

      const browser = await adapter.launch(options);

      expect(mockPuppeteer.launch).to.have.been.calledWith({
        headless: true,
        args: ['--no-sandbox'],
        executablePath: '/path/to/chrome',
        devtools: false,
        slowMo: 0
      });

      expect(browser).to.be.instanceOf(PuppeteerBrowserAdapter);
    });

    it('should remove null values from options', async () => {
      const mockBrowser = { test: 'browser' };
      mockPuppeteer.launch.resolves(mockBrowser);

      const options = {
        headless: true,
        executablePath: null
      };

      await adapter.launch(options);

      const calledOptions = mockPuppeteer.launch.getCall(0).args[0];
      expect(calledOptions).to.not.have.property('executablePath');
    });
  });

  describe('detectBrowsers', () => {
    it('should detect available browsers', async () => {
      const mockBrowserFetcher = {
        localRevisions: sinon.stub().resolves(['revision1', 'revision2']),
        revisionInfo: sinon.stub()
      };

      mockBrowserFetcher.revisionInfo
        .withArgs('revision1').returns({ executablePath: '/path1' })
        .withArgs('revision2').returns({ executablePath: '/path2' });

      mockPuppeteer.createBrowserFetcher.returns(mockBrowserFetcher);

      const browsers = await adapter.detectBrowsers();

      expect(browsers).to.have.length(2);
      expect(browsers[0]).to.deep.include({
        name: 'Chrome (Puppeteer)',
        type: 'puppeteer',
        executablePath: '/path1',
        version: 'revision1',
        available: true
      });
    });

    it('should return empty array on error', async () => {
      mockPuppeteer.createBrowserFetcher.throws(new Error('Test error'));

      const browsers = await adapter.detectBrowsers();
      expect(browsers).to.be.an('array').that.is.empty;
    });
  });

  describe('downloadBrowser', () => {
    it('should download browser successfully', async () => {
      const mockBrowserFetcher = {
        download: sinon.stub().resolves()
      };

      mockPuppeteer.createBrowserFetcher.returns(mockBrowserFetcher);

      const result = await adapter.downloadBrowser();

      expect(result).to.be.true;
      expect(mockBrowserFetcher.download).to.have.been.calledWith('test-revision');
    });

    it('should return false on download error', async () => {
      const mockBrowserFetcher = {
        download: sinon.stub().rejects(new Error('Download failed'))
      };

      mockPuppeteer.createBrowserFetcher.returns(mockBrowserFetcher);

      const result = await adapter.downloadBrowser();
      expect(result).to.be.false;
    });
  });

  describe('getBrowserType', () => {
    it('should return puppeteer', () => {
      expect(adapter.getBrowserType()).to.equal('puppeteer');
    });
  });
});

describe('PuppeteerBrowserAdapter', () => {
  let adapter;
  let mockBrowser;

  beforeEach(() => {
    mockBrowser = {
      newPage: sinon.stub(),
      pages: sinon.stub(),
      close: sinon.stub(),
      version: sinon.stub()
    };
    
    adapter = new PuppeteerBrowserAdapter(mockBrowser);
  });

  describe('newPage', () => {
    it('should create new page and return adapter', async () => {
      const mockPage = { test: 'page' };
      mockBrowser.newPage.resolves(mockPage);

      const page = await adapter.newPage();

      expect(page).to.be.instanceOf(PuppeteerPageAdapter);
      expect(mockBrowser.newPage).to.have.been.called;
    });
  });

  describe('pages', () => {
    it('should return array of page adapters', async () => {
      const mockPages = [{ test: 'page1' }, { test: 'page2' }];
      mockBrowser.pages.resolves(mockPages);

      const pages = await adapter.pages();

      expect(pages).to.have.length(2);
      expect(pages[0]).to.be.instanceOf(PuppeteerPageAdapter);
      expect(pages[1]).to.be.instanceOf(PuppeteerPageAdapter);
    });
  });

  describe('close', () => {
    it('should close browser', async () => {
      await adapter.close();
      expect(mockBrowser.close).to.have.been.called;
    });
  });

  describe('version', () => {
    it('should return browser version', async () => {
      mockBrowser.version.resolves('1.0.0');
      
      const version = await adapter.version();
      expect(version).to.equal('1.0.0');
    });
  });
});

describe('PuppeteerPageAdapter', () => {
  let adapter;
  let mockPage;

  beforeEach(() => {
    mockPage = {
      goto: sinon.stub(),
      url: sinon.stub(),
      setViewport: sinon.stub(),
      screenshot: sinon.stub(),
      evaluate: sinon.stub(),
      waitForSelector: sinon.stub(),
      waitForFunction: sinon.stub(),
      $: sinon.stub(),
      $$: sinon.stub(),
      setDefaultTimeout: sinon.stub(),
      setDefaultNavigationTimeout: sinon.stub(),
      on: sinon.stub(),
      off: sinon.stub(),
      close: sinon.stub()
    };
    
    adapter = new PuppeteerPageAdapter(mockPage);
  });

  describe('goto', () => {
    it('should navigate to URL with options', async () => {
      const mockResponse = { status: () => 200 };
      mockPage.goto.resolves(mockResponse);

      const response = await adapter.goto('http://example.com', {
        waitUntil: 'networkidle0',
        timeout: 5000
      });

      expect(mockPage.goto).to.have.been.calledWith('http://example.com', {
        waitUntil: 'networkidle0',
        timeout: 5000
      });

      expect(response).to.be.instanceOf(PuppeteerResponseAdapter);
    });

    it('should return null if no response', async () => {
      mockPage.goto.resolves(null);

      const response = await adapter.goto('http://example.com');
      expect(response).to.be.null;
    });
  });

  describe('waitForSelector', () => {
    it('should wait for selector and return element adapter', async () => {
      const mockElement = { test: 'element' };
      mockPage.waitForSelector.resolves(mockElement);

      const element = await adapter.waitForSelector('.test', {
        visible: true,
        timeout: 5000
      });

      expect(mockPage.waitForSelector).to.have.been.calledWith('.test', {
        visible: true,
        timeout: 5000
      });

      expect(element).to.be.instanceOf(PuppeteerElementAdapter);
    });

    it('should return null if no element found', async () => {
      mockPage.waitForSelector.resolves(null);

      const element = await adapter.waitForSelector('.test');
      expect(element).to.be.null;
    });
  });
});
