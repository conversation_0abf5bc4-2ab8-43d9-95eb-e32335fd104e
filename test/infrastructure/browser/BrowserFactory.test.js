const { BrowserFactory } = require('../../../src/infrastructure/browser/BrowserFactory');
const { PuppeteerAdapter } = require('../../../src/infrastructure/browser/PuppeteerAdapter');
const { PlaywrightAdapter } = require('../../../src/infrastructure/browser/PlaywrightAdapter');

describe('BrowserFactory', () => {
  let factory;

  beforeEach(() => {
    factory = new BrowserFactory();
  });

  describe('constructor', () => {
    it('should initialize with default adapters', () => {
      const availableTypes = factory.getAvailableTypes();
      expect(availableTypes).toContain('puppeteer');
      expect(availableTypes).toContain('playwright-chromium');
      expect(availableTypes).toContain('playwright-firefox');
      expect(availableTypes).toContain('playwright-webkit');
    });

    it('should set default adapter to puppeteer', () => {
      expect(factory.getDefaultAdapter()).toBe('puppeteer');
    });
  });

  describe('registerAdapter', () => {
    it('should register a new adapter', () => {
      const mockAdapter = class MockAdapter {};
      factory.registerAdapter('mock', mockAdapter);

      const availableTypes = factory.getAvailableTypes();
      expect(availableTypes).toContain('mock');
    });
  });

  describe('createBrowserAutomation', () => {
    it('should create puppeteer adapter by default', () => {
      const adapter = factory.createBrowserAutomation();
      expect(adapter).toBeInstanceOf(PuppeteerAdapter);
    });

    it('should create specified adapter type', () => {
      const adapter = factory.createBrowserAutomation('puppeteer');
      expect(adapter).toBeInstanceOf(PuppeteerAdapter);
    });

    it('should throw error for unsupported adapter type', () => {
      expect(() => {
        factory.createBrowserAutomation('unsupported');
      }).toThrow('Unsupported browser automation type: unsupported');
    });

    it('should create playwright adapter', () => {
      try {
        const adapter = factory.createBrowserAutomation('playwright-chromium');
        expect(adapter).toBeInstanceOf(PlaywrightAdapter);
      } catch (error) {
        // Skip test if Playwright is not installed
        expect(error.message).toContain('Playwright is not installed');
      }
    });
  });

  describe('setDefaultAdapter', () => {
    it('should set default adapter', () => {
      factory.setDefaultAdapter('playwright-chromium');
      expect(factory.getDefaultAdapter()).toBe('playwright-chromium');
    });

    it('should throw error for unknown adapter type', () => {
      expect(() => {
        factory.setDefaultAdapter('unknown');
      }).toThrow('Unknown adapter type: unknown');
    });
  });

  describe('detectAvailableBrowsers', () => {
    it('should return an array', async () => {
      const browsers = await factory.detectAvailableBrowsers();
      expect(Array.isArray(browsers)).toBe(true);
    });
  });

  describe('selectBestAdapter', () => {
    it('should throw error if no browsers available', async () => {
      // Mock empty browser list
      const originalMethod = factory.detectAvailableBrowsers;
      factory.detectAvailableBrowsers = jest.fn().mockResolvedValue([]);

      await expect(factory.selectBestAdapter()).rejects.toThrow('No browsers available');

      // Restore original method
      factory.detectAvailableBrowsers = originalMethod;
    });
  });

  describe('createConfiguredBrowserAutomation', () => {
    it('should use specified type', async () => {
      const adapter = await factory.createConfiguredBrowserAutomation({
        type: 'puppeteer'
      });
      expect(adapter).toBeInstanceOf(PuppeteerAdapter);
    });

    it('should fallback to default on auto-select failure', async () => {
      // Mock selectBestAdapter to fail
      const originalMethod = factory.selectBestAdapter;
      factory.selectBestAdapter = jest.fn().mockRejectedValue(new Error('Selection failed'));

      const adapter = await factory.createConfiguredBrowserAutomation({
        autoSelect: true
      });
      expect(adapter).toBeInstanceOf(PuppeteerAdapter);

      // Restore original method
      factory.selectBestAdapter = originalMethod;
    });
  });
});
