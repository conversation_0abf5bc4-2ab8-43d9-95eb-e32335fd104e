const { expect } = require('chai');
const sinon = require('sinon');
const { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Playwright<PERSON><PERSON>er<PERSON>dapter, 
  PlaywrightPageAdapter,
  PlaywrightElementAdapter,
  PlaywrightResponseAdapter
} = require('../../../src/infrastructure/browser/PlaywrightAdapter');

describe('PlaywrightAdapter', () => {
  let adapter;
  let mockChromium;

  beforeEach(() => {
    mockChromium = {
      launch: sinon.stub(),
      executablePath: sinon.stub().returns('/path/to/chromium')
    };
    
    adapter = new PlaywrightAdapter('chromium');
    adapter.playwright = mockChromium;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('constructor', () => {
    it('should default to chromium browser type', () => {
      const defaultAdapter = new PlaywrightAdapter();
      expect(defaultAdapter.browserType).to.equal('chromium');
    });

    it('should accept custom browser type', () => {
      const firefoxAdapter = new PlaywrightAdapter('firefox');
      expect(firefoxAdapter.browserType).to.equal('firefox');
    });
  });

  describe('launch', () => {
    it('should launch browser with correct options', async () => {
      const mockBrowser = { test: 'browser' };
      mockChromium.launch.resolves(mockBrowser);

      const options = {
        headless: true,
        args: ['--no-sandbox'],
        executablePath: '/path/to/chrome'
      };

      const browser = await adapter.launch(options);

      expect(mockChromium.launch).to.have.been.calledWith({
        headless: true,
        args: ['--no-sandbox'],
        executablePath: '/path/to/chrome',
        devtools: false,
        slowMo: 0
      });

      expect(browser).to.be.instanceOf(PlaywrightBrowserAdapter);
    });

    it('should remove null values from options', async () => {
      const mockBrowser = { test: 'browser' };
      mockChromium.launch.resolves(mockBrowser);

      const options = {
        headless: true,
        executablePath: null
      };

      await adapter.launch(options);

      const calledOptions = mockChromium.launch.getCall(0).args[0];
      expect(calledOptions).to.not.have.property('executablePath');
    });
  });

  describe('detectBrowsers', () => {
    it('should detect available browsers', async () => {
      // Mock successful browser launches for detection
      const mockBrowser = {
        close: sinon.stub().resolves()
      };

      mockChromium.launch.resolves(mockBrowser);
      mockChromium.executablePath.returns('/path/to/chromium');

      const browsers = await adapter.detectBrowsers();

      expect(browsers).to.have.length(1);
      expect(browsers[0]).to.deep.include({
        name: 'Chromium (Playwright)',
        type: 'playwright-chromium',
        executablePath: '/path/to/chromium',
        version: 'latest',
        available: true
      });

      expect(mockBrowser.close).to.have.been.called;
    });

    it('should return empty array on error', async () => {
      mockChromium.launch.rejects(new Error('Browser not available'));

      const browsers = await adapter.detectBrowsers();
      expect(browsers).to.be.an('array').that.is.empty;
    });
  });

  describe('downloadBrowser', () => {
    it('should return true (browsers auto-downloaded)', async () => {
      const result = await adapter.downloadBrowser();
      expect(result).to.be.true;
    });
  });

  describe('getBrowserType', () => {
    it('should return playwright', () => {
      expect(adapter.getBrowserType()).to.equal('playwright');
    });
  });
});

describe('PlaywrightBrowserAdapter', () => {
  let adapter;
  let mockBrowser;

  beforeEach(() => {
    mockBrowser = {
      newPage: sinon.stub(),
      contexts: sinon.stub(),
      close: sinon.stub(),
      version: sinon.stub()
    };
    
    adapter = new PlaywrightBrowserAdapter(mockBrowser);
  });

  describe('newPage', () => {
    it('should create new page and return adapter', async () => {
      const mockPage = { test: 'page' };
      mockBrowser.newPage.resolves(mockPage);

      const page = await adapter.newPage();

      expect(page).to.be.instanceOf(PlaywrightPageAdapter);
      expect(mockBrowser.newPage).to.have.been.called;
    });
  });

  describe('pages', () => {
    it('should return array of page adapters from all contexts', async () => {
      const mockContext1 = {
        pages: sinon.stub().returns([{ test: 'page1' }])
      };
      const mockContext2 = {
        pages: sinon.stub().returns([{ test: 'page2' }])
      };

      mockBrowser.contexts.returns([mockContext1, mockContext2]);

      const pages = await adapter.pages();

      expect(pages).to.have.length(2);
      expect(pages[0]).to.be.instanceOf(PlaywrightPageAdapter);
      expect(pages[1]).to.be.instanceOf(PlaywrightPageAdapter);
    });
  });

  describe('close', () => {
    it('should close browser', async () => {
      await adapter.close();
      expect(mockBrowser.close).to.have.been.called;
    });
  });

  describe('version', () => {
    it('should return browser version', () => {
      mockBrowser.version.returns('1.0.0');
      
      const version = adapter.version();
      expect(version).to.equal('1.0.0');
    });
  });
});

describe('PlaywrightPageAdapter', () => {
  let adapter;
  let mockPage;

  beforeEach(() => {
    mockPage = {
      goto: sinon.stub(),
      url: sinon.stub(),
      setViewportSize: sinon.stub(),
      screenshot: sinon.stub(),
      evaluate: sinon.stub(),
      waitForSelector: sinon.stub(),
      waitForFunction: sinon.stub(),
      $: sinon.stub(),
      $$: sinon.stub(),
      setDefaultTimeout: sinon.stub(),
      setDefaultNavigationTimeout: sinon.stub(),
      on: sinon.stub(),
      off: sinon.stub(),
      close: sinon.stub()
    };
    
    adapter = new PlaywrightPageAdapter(mockPage);
  });

  describe('goto', () => {
    it('should navigate to URL with options', async () => {
      const mockResponse = { status: () => 200 };
      mockPage.goto.resolves(mockResponse);

      const response = await adapter.goto('http://example.com', {
        waitUntil: 'domcontentloaded',
        timeout: 5000
      });

      expect(mockPage.goto).to.have.been.calledWith('http://example.com', {
        waitUntil: 'domcontentloaded',
        timeout: 5000
      });

      expect(response).to.be.instanceOf(PlaywrightResponseAdapter);
    });

    it('should convert networkidle0 to networkidle', async () => {
      const mockResponse = { status: () => 200 };
      mockPage.goto.resolves(mockResponse);

      await adapter.goto('http://example.com', {
        waitUntil: 'networkidle0'
      });

      expect(mockPage.goto).to.have.been.calledWith('http://example.com', {
        waitUntil: 'networkidle',
        timeout: 30000
      });
    });

    it('should return null if no response', async () => {
      mockPage.goto.resolves(null);

      const response = await adapter.goto('http://example.com');
      expect(response).to.be.null;
    });
  });

  describe('setViewport', () => {
    it('should call setViewportSize', async () => {
      const viewport = { width: 1920, height: 1080 };
      
      await adapter.setViewport(viewport);
      
      expect(mockPage.setViewportSize).to.have.been.calledWith(viewport);
    });
  });

  describe('waitForSelector', () => {
    it('should wait for selector with visible state', async () => {
      const mockElement = { test: 'element' };
      mockPage.waitForSelector.resolves(mockElement);

      const element = await adapter.waitForSelector('.test', {
        visible: true,
        timeout: 5000
      });

      expect(mockPage.waitForSelector).to.have.been.calledWith('.test', {
        state: 'visible',
        timeout: 5000
      });

      expect(element).to.be.instanceOf(PlaywrightElementAdapter);
    });

    it('should return null if no element found', async () => {
      mockPage.waitForSelector.resolves(null);

      const element = await adapter.waitForSelector('.test');
      expect(element).to.be.null;
    });
  });
});

describe('PlaywrightElementAdapter', () => {
  let adapter;
  let mockElement;

  beforeEach(() => {
    mockElement = {
      click: sinon.stub(),
      fill: sinon.stub(),
      type: sinon.stub(),
      textContent: sinon.stub(),
      getAttribute: sinon.stub()
    };
    
    adapter = new PlaywrightElementAdapter(mockElement);
  });

  describe('type', () => {
    it('should use fill when no delay specified', async () => {
      await adapter.type('test text', {});
      
      expect(mockElement.fill).to.have.been.calledWith('test text');
      expect(mockElement.type).to.not.have.been.called;
    });

    it('should use type when delay specified', async () => {
      await adapter.type('test text', { delay: 100 });
      
      expect(mockElement.type).to.have.been.calledWith('test text', { delay: 100 });
      expect(mockElement.fill).to.not.have.been.called;
    });
  });
});
