/**
 * 浏览器抽象层使用示例
 * 
 * 这个示例展示了如何使用新的浏览器抽象层来替换 Puppeteer，
 * 支持在 Puppeteer 和 Playwright 之间无缝切换。
 */

const { browserFactory } = require('../src/infrastructure/browser');

async function basicUsageExample() {
  console.log('=== 基本使用示例 ===');
  
  // 1. 使用默认的浏览器自动化工具（Puppeteer）
  const defaultAdapter = browserFactory.createBrowserAutomation();
  console.log('默认适配器类型:', defaultAdapter.getBrowserType());
  
  // 2. 明确指定使用 Puppeteer
  const puppeteerAdapter = browserFactory.createBrowserAutomation('puppeteer');
  console.log('Puppeteer 适配器类型:', puppeteerAdapter.getBrowserType());
  
  // 3. 尝试使用 Playwright（如果已安装）
  try {
    const playwrightAdapter = browserFactory.createBrowserAutomation('playwright-chromium');
    console.log('Playwright 适配器类型:', playwrightAdapter.getBrowserType());
  } catch (error) {
    console.log('Playwright 未安装:', error.message);
  }
}

async function browserDetectionExample() {
  console.log('\n=== 浏览器检测示例 ===');
  
  // 检测所有可用的浏览器
  const availableBrowsers = await browserFactory.detectAvailableBrowsers();
  console.log('检测到的浏览器数量:', availableBrowsers.length);
  
  availableBrowsers.forEach((browser, index) => {
    console.log(`${index + 1}. ${browser.name} (${browser.adapterType})`);
    console.log(`   路径: ${browser.executablePath}`);
    console.log(`   版本: ${browser.version}`);
    console.log(`   可用: ${browser.available}`);
  });
}

async function autoSelectionExample() {
  console.log('\n=== 自动选择示例 ===');
  
  try {
    // 自动选择最佳的浏览器适配器
    const bestAdapter = await browserFactory.createConfiguredBrowserAutomation({
      autoSelect: true,
      preferredTypes: ['playwright-chromium', 'puppeteer']
    });
    
    console.log('自动选择的适配器:', bestAdapter.getBrowserType());
  } catch (error) {
    console.log('自动选择失败:', error.message);
  }
}

async function launchBrowserExample() {
  console.log('\n=== 启动浏览器示例 ===');
  
  try {
    // 创建浏览器适配器
    const adapter = browserFactory.createBrowserAutomation('puppeteer');
    
    // 启动浏览器
    const browser = await adapter.launch({
      headless: true,
      args: ['--no-sandbox']
    });
    
    console.log('浏览器启动成功');
    
    // 创建新页面
    const page = await browser.newPage();
    console.log('新页面创建成功');
    
    // 导航到页面
    const response = await page.goto('https://example.com');
    if (response) {
      console.log('页面导航成功，状态码:', response.status());
    }
    
    // 获取页面标题
    const title = await page.evaluate(() => document.title);
    console.log('页面标题:', title);
    
    // 关闭浏览器
    await browser.close();
    console.log('浏览器已关闭');
    
  } catch (error) {
    console.log('浏览器操作失败:', error.message);
  }
}

async function migrationExample() {
  console.log('\n=== 迁移示例：从直接使用 Puppeteer 到使用抽象层 ===');
  
  // 旧的方式（直接使用 Puppeteer）
  console.log('旧的方式:');
  console.log(`
  const puppeteer = require('puppeteer');
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  await page.goto('https://example.com');
  await browser.close();
  `);
  
  // 新的方式（使用抽象层）
  console.log('新的方式:');
  console.log(`
  const { browserFactory } = require('./src/infrastructure/browser');
  const adapter = browserFactory.createBrowserAutomation('puppeteer');
  const browser = await adapter.launch({ headless: true });
  const page = await browser.newPage();
  await page.goto('https://example.com');
  await browser.close();
  `);
  
  console.log('优势:');
  console.log('1. 统一的 API 接口');
  console.log('2. 支持多种浏览器自动化工具');
  console.log('3. 可以轻松切换实现');
  console.log('4. 更好的错误处理');
  console.log('5. 自动浏览器检测和选择');
}

async function configurationExample() {
  console.log('\n=== 配置示例 ===');
  
  // 注册自定义适配器
  class CustomAdapter {
    getBrowserType() {
      return 'custom';
    }
    
    async launch() {
      throw new Error('Custom adapter not implemented');
    }
  }
  
  browserFactory.registerAdapter('custom', CustomAdapter);
  console.log('自定义适配器已注册');
  
  // 设置默认适配器
  const originalDefault = browserFactory.getDefaultAdapter();
  console.log('原始默认适配器:', originalDefault);
  
  try {
    browserFactory.setDefaultAdapter('playwright-chromium');
    console.log('新的默认适配器:', browserFactory.getDefaultAdapter());
  } catch (error) {
    console.log('设置默认适配器失败:', error.message);
  }
  
  // 恢复原始默认适配器
  browserFactory.setDefaultAdapter(originalDefault);
  console.log('恢复默认适配器:', browserFactory.getDefaultAdapter());
  
  // 显示所有可用的适配器类型
  console.log('所有可用的适配器类型:', browserFactory.getAvailableTypes());
}

async function main() {
  console.log('浏览器抽象层使用示例\n');
  
  try {
    await basicUsageExample();
    await browserDetectionExample();
    await autoSelectionExample();
    await launchBrowserExample();
    await migrationExample();
    await configurationExample();
    
    console.log('\n=== 示例完成 ===');
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  main();
}

module.exports = {
  basicUsageExample,
  browserDetectionExample,
  autoSelectionExample,
  launchBrowserExample,
  migrationExample,
  configurationExample
};
