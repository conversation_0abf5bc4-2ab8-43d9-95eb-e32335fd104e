<template>
  <div id="app">
    <el-button @click="handleClick">Hello World</el-button>
    <HelloWorld :message="message" />
  </div>
</template>

<script>
import HelloWorld from './components/HelloWorld.vue'

export default {
  name: 'App',
  components: {
    HelloWorld
  },
  data() {
    return {
      message: 'Hello Vue 2!'
    }
  },
  methods: {
    handleClick() {
      this.$message.success('Button clicked!')
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>
