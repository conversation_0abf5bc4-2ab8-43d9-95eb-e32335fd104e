<template>
  <div class="dashboard">
    <h1>Dashboard</h1>
    <Chart :data="chartData" />
  </div>
</template>

<script>
import Chart from '@/components/Chart'

export default {
  name: 'Dashboard',
  components: {
    Chart
  },
  data() {
    return {
      chartData: {
        labels: ['January', 'February', 'March'],
        datasets: [{
          label: 'Sales',
          data: [12, 19, 3]
        }]
      }
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}
</style>
