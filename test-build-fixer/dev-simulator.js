#!/usr/bin/env node

console.log("Dev server starting...");
console.log("Local:   http://localhost:3000/");
console.log("Network: http://*************:3000/");

setTimeout(() => {
  console.log("");
  console.log("ERROR in src/main.js:2:0-21");
  console.log("Module not found: Error: Can't resolve 'element-ui'");
  console.log("");
  console.log("ERROR: Failed to compile with 1 error");
  process.exit(1);
}, 2000);
