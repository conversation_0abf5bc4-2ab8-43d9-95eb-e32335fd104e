#!/usr/bin/env node

// 模拟真实的 Vue 构建错误输出
const errors = [
  "ERROR in src/main.js:3:0-21",
  "Module not found: Error: Can't resolve 'element-ui' in '/Users/<USER>/works/galaxy/migrate/migrate-cli/test-build-fixer/src'",
  "",
  "ERROR in src/main.js:1:0-15",
  "Module not found: Error: Can't resolve 'vue' in '/Users/<USER>/works/galaxy/migrate/migrate-cli/test-build-fixer/src'",
  "",
  "ERROR in src/router/index.js:2:0-21",
  "Module not found: Error: Can't resolve 'vue-router' in '/Users/<USER>/works/galaxy/migrate/migrate-cli/test-build-fixer/src/router'",
  "",
  "src/main.js(9,1): error TS2304: Cannot find name 'Vue'.",
  "src/main.js(11,5): error TS2339: Property '$mount' does not exist on type 'App<Element>'.",
  "",
  "ERROR in src/views/Dashboard.vue:9:0-32",
  "Module not found: Error: Can't resolve '@/components/Chart' in '/Users/<USER>/works/galaxy/migrate/migrate-cli/test-build-fixer/src/views'",
  "",
  "ERROR in src/components/HelloWorld.vue:6:8",
  "Property 'slot' does not exist on type 'IntrinsicAttributes'",
  "",
  "webpack compiled with 6 errors",
  "",
  "Build failed with compilation errors.",
  "Please check the errors above and fix them before proceeding."
];

console.log(errors.join('\n'));
process.exit(1);
