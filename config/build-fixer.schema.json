{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Build Fixer Configuration", "description": "Configuration schema for Vue Build Fixer", "type": "object", "properties": {"buildCommand": {"type": "string", "description": "Command to build the project", "default": "npm run build"}, "installCommand": {"type": "string", "description": "Command to install dependencies", "default": "pnpm install"}, "maxAttempts": {"type": "integer", "description": "Maximum number of fix attempts", "minimum": 1, "maximum": 10, "default": 3}, "useLegacyPeerDeps": {"type": "boolean", "description": "Use --legacy-peer-deps when installing dependencies", "default": true}, "skipInstall": {"type": "boolean", "description": "Skip dependency installation", "default": false}, "skipAI": {"type": "boolean", "description": "Skip AI-powered fixes", "default": false}, "dryRun": {"type": "boolean", "description": "Preview mode - don't modify files", "default": false}, "verbose": {"type": "boolean", "description": "Show detailed output", "default": false}, "showProgress": {"type": "boolean", "description": "Show progress indicators", "default": true}, "errorPatterns": {"type": "object", "description": "Regex patterns for different error types", "properties": {"typescript": {"type": "array", "items": {"type": "string"}}, "vue": {"type": "array", "items": {"type": "string"}}, "webpack": {"type": "array", "items": {"type": "string"}}, "eslint": {"type": "array", "items": {"type": "string"}}}}, "fixStrategies": {"type": "object", "description": "Configuration for different fix strategies", "patternProperties": {".*": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "priority": {"type": "integer", "minimum": 1}, "useAI": {"type": "boolean"}}, "required": ["enabled", "priority"]}}}, "aiConfig": {"type": "object", "description": "AI service configuration", "properties": {"maxTokens": {"type": "integer", "minimum": 1000, "maximum": 32000, "default": 8000}, "temperature": {"type": "number", "minimum": 0, "maximum": 2, "default": 0.0}, "maxRetries": {"type": "integer", "minimum": 1, "maximum": 10, "default": 3}, "timeout": {"type": "integer", "minimum": 5000, "maximum": 300000, "default": 30000}}}, "moduleMapping": {"type": "object", "description": "Module replacement mappings", "patternProperties": {".*": {"type": "string"}}}, "excludePatterns": {"type": "array", "description": "File patterns to exclude from processing", "items": {"type": "string"}}, "backupConfig": {"type": "object", "description": "File backup configuration", "properties": {"enabled": {"type": "boolean", "default": true}, "suffix": {"type": "string", "default": "build-fixer-backup"}, "maxBackups": {"type": "integer", "minimum": 1, "default": 5}}}, "logging": {"type": "object", "description": "Logging configuration", "properties": {"level": {"type": "string", "enum": ["debug", "info", "warn", "error"], "default": "info"}, "logFile": {"type": "string", "default": "build-fixer.log"}, "enableFileLogging": {"type": "boolean", "default": false}}}}}