{"$schema": "./build-fixer.schema.json", "buildCommand": "pnpm run build", "installCommand": "pnpm install", "maxAttempts": 10, "useLegacyPeerDeps": true, "skipInstall": false, "skipAI": false, "dryRun": false, "verbose": false, "showProgress": true, "errorPatterns": {"typescript": ["error TS\\d+:", "\\.ts\\(\\d+,\\d+\\):"], "vue": ["\\.vue:\\d+:\\d+:", "Vue warn"], "webpack": ["ERROR in", "<PERSON><PERSON><PERSON> not found"], "eslint": ["\\d+:\\d+\\s+(error|warning)"]}, "fixStrategies": {"missing-module": {"enabled": true, "priority": 1, "useAI": false}, "property-not-exist": {"enabled": true, "priority": 2, "useAI": true}, "vue-version": {"enabled": true, "priority": 3, "useAI": true}, "ui-library": {"enabled": true, "priority": 4, "useAI": true}}, "aiConfig": {"maxTokens": 8000, "temperature": 0.0, "maxRetries": 10, "timeout": 30000}, "moduleMapping": {"vue": "vue@^3.0.0", "vue-router": "vue-router@^4.0.0", "vuex": "@pinia/nuxt", "element-ui": "element-plus"}, "excludePatterns": ["node_modules/**", "dist/**", "build/**", "*.min.js", "*.map"], "backupConfig": {"enabled": true, "suffix": "build-fixer-backup", "maxBackups": 5}, "logging": {"level": "info", "logFile": "build-fixer.log", "enableFileLogging": false}}