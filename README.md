我正在设计一个 Vue 2 到 Vue 3 的规模化迁移方案，我需要你根据我的 task 编写迁移脚本。相关信息：

- aup-admin-ui 是 Vue 2 迁移的代码库
- vue-elment-admin 是 aup-admin-ui  原来的模板应用
- vue3-element-plus-admin 是新的工程模板（你要工作在这个工程中）
- migrate-cli 是未来我要编写的迁移脚本，将使用 gogocode 进行代码迁移，因此需要对齐原来的工程。

我的目标技术栈是： TypeScript/Javascript + vuex + vue router 4 + element-plus


Todos: 

- [ ] Gogocode 问题
  - https://github.com/thx/gogocode/issues/234 
- [ ] Vue Router 4 迁移
- [ ] Vite 脚本 ？ `@vitejs/plugin-vue`
- [ ] `@element-plus/icons -> `@element-plus/icons-vue` // 手动引入
- [ ]  '@/utils/gogocodeTransfer': resolve('src/lib/gogocodeTransfer.js') // 修改这里为你 
- [ ] `pnpm install path-browserify stream-browserify` 
- [ ] `pnpm install @vue/preload-webpack-plugin`
- [ ] 插件兼容层：`vuedrggable`
- [ ] vue.config.js
```javascript
module.exports = {
	configureWebpack: {
		name: 'vue Element Admin',
		resolve: {
			alias: {
				'@': resolve('src'),
				'@/utils/gogocodeTransfer': resolve('src/lib/gogocodeTransfer.js') // 修改这里为你
			},
			fallback: {
				"stream": require.resolve("stream-browserify"),
				"path": require.resolve("path-browserify"),
			}
		},
		plugins: [
			// 引入 VuePreloadPlugin 替代 PreloadWebpackPlugin
			new (require('@vue/preload-webpack-plugin'))({
				rel: 'preload',
				fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
				include: 'initial'
			})
		]
	},
	chainWebpack: config => {
		config.plugins.delete('prefetch');
		config.module
			.rule('svg')
			.exclude.add(resolve('src/icons'))
			.end();
		config.module
			.rule('icons')
			.test(/\.svg$/)
			.include.add(resolve('src/icons'))
			.end()
			.use('svg-sprite-loader')
			.loader('svg-sprite-loader')
			.options({
				symbolId: 'icon-[name]'
			})
			.end();
		config
			.when(process.env.NODE_ENV !== 'development',
				config => {
					config.optimization.splitChunks({
						chunks: 'all',
						cacheGroups: {
							libs: {
								name: 'chunk-libs',
								test: /[\\/]node_modules[\\/]/,
								priority: 10,
								chunks: 'initial'
							},
							elementUI: {
								name: 'chunk-elementUI',
								priority: 20,
								test: /[\\/]node_modules[\\/]_?element-ui(.*)/
							},
							commons: {
								name: 'chunk-commons',
								test: resolve('src/components'),
								minChunks: 3,
								priority: 5,
								reuseExistingChunk: true
							}
						}
					});
					config.optimization.runtimeChunk('single');
				}
			);
	},
	publicPath: '/',
	outputDir: 'dist',
	assetsDir: 'static',
	lintOnSave: process.env.NODE_ENV === 'development',
	productionSourceMap: false,
	devServer: {
		port: process.env.port || process.env.npm_config_port || 9527,
		open: true,
		overlay: {
			warnings: false,
			errors: true
		},
		before: require('./mock/mock-server.js')
	}
}

function resolve(dir) {
	const path = require('path');
	return path.join(__dirname, dir);
}

const name = process.env.VUE_APP_TITLE || 'vue Element Admin';
```

Module not found: Error: Can't resolve '@/utils/gogocodeTransfer' in '/Users/<USER>/works/galaxy/vue3-element-plus/src/views/example/components/Dropdown'

error  in ./src/views/example/list.vue?vue&type=script&lang=js

error  in ./src/views/excel/components/AutoWidthOption.vue?vue&type=script&lang=js

Module not found: Error: Can't resolve '@/utils/gogocodeTransfer' in '/Users/<USER>/works/galaxy/vue3-element-plus/src/views/excel/components'

error  in ./src/views/excel/components/FilenameOption.vue?vue&type=script&lang=js

Module not found: Error: Can't resolve '@/utils/gogocodeTransfer' in '/Users/<USER>/works/galaxy/vue3-element-plus/src/views/permission/components'


## ✅ 1. 使用 Gogocode 转换 `package.json` 中 Vue 相关依赖版本

**目标**：将 `vue`, `vue-template-compiler`, `vue-router`, `vuex` 等升级为 Vue 3 兼容版本。

**实现建议**：

* 使用 `gogocode` 修改 JSON 文件结构（也可直接用 JS 操作）
* 或使用 Node 脚本处理 `package.json`

**示例代码**（Node）：

```js
const fs = require('fs');
const pkg = require('./package.json');

pkg.dependencies.vue = '^3.4.0';
delete pkg.dependencies['vue-template-compiler'];
pkg.devDependencies['@vue/compiler-sfc'] = '^3.4.0';

fs.writeFileSync('./package.json', JSON.stringify(pkg, null, 2));
```

---

## ✅ 2. 检查每个依赖是否存在 Vue 3 的兼容版本（可选）

**目标**：提前排查不兼容插件，有没有可能结合 AI 检查哪些是 Vue 的组件，如果配置了 AI 的话。

**做法**：

* 使用 `npm view <pkg> peerDependencies` 查看是否支持 Vue 3
* 可结合 [`npm-check-updates`](https://www.npmjs.com/package/npm-check-updates)

**示例代码**：

```bash
npx npm-check-updates -u
npm install
```

或检查某依赖的最新版本信息：

```bash
npm view vue-router versions
```

---

## ✅ 3. 批量使用 Gogocode 将 `.vue` 和 `.js` 文件迁移到 Vue 3

**目标**：将 Vue 2 的语法（如 `this.$refs`, `this.$emit`, `options API`）迁移为 Composition API 等。

**做法**：

* 使用 [Gogocode AST 转换脚本](https://github.com/thx/gogocode)
* 可以用现成的 Vue 2 ➝ 3 preset，或自己编写转换器

```js
const elementUiTransform = require('@unit-mesh/gogocode-plugin-element')

const options = {
	outRootPath: path.dirname(targetDir, '.'),
}

const vueResult = vue2To3Transform.transform({
	source,
	path: filePath,
	options
}, {
	gogocode: require('gogocode')
}, {})

```

---

## ✅ 4. 记录转换失败的文件

**目标**：识别失败文件，供后续 AI 修复


## ✅ 5. 使用 AI 库（如 ChatGPT API）修复失败文件

**目标**：自动修复 Gogocode 无法迁移的边缘文件

**推荐**：

* 使用 Deepssek 等自动调用修复失败代码
* Prompt 应包括 Vue 2 和目标 Vue 3 的背景提示

**示例（伪代码）**：

```js
import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

const { text } = await generateText({
    model: openai("gpt-4o"),
    prompt: prompt,
    maxTokens: 4000,
    })

///     
```



---

## ✅ 6. 使用 ESLint 自动修复格式和语法

**目标**：统一格式，修复可能的语法问题


## ✅ 7. 构建项目并使用 AI 修复构建错误

**目标**：完成构建，进一步修复遗留问题（如 TypeError, undefined 等）

**做法**：

* 执行 `vite build` 或 `webpack build`
* 捕捉构建错误输出，结合 AI 自动修复

## ✅ 8. 智能文件复制功能 🧠

**目标**：在迁移过程中智能识别并复制项目运行所需的文件和目录

**特性**：
* 🤖 AI 驱动分析：使用 AI 分析项目结构，识别真正需要的文件
* 📁 默认策略：当 AI 不可用时，使用预设策略复制常见必要文件
* 🔍 智能过滤：自动排除不必要的文件（node_modules、dist、.git 等）
* 🎯 针对性复制：避免遗漏重要的配置和资源文件

**AI 模式会分析：**
- mock 数据目录（如 mock/、tests/fixtures/）
- 静态资源目录（如 public/、static/、assets/）
- 配置文件（vue.config.js、vite.config.js、babel.config.js 等）
- 环境配置文件（.env.*）
- 脚本文件目录（如 scripts/、build/）

**默认策略包含：**
- 配置文件：vue.config.js, babel.config.js, .eslintrc.js, tsconfig.json
- 环境文件：.env, .env.development, .env.production
- 目录：mock/, public/, static/, assets/, scripts/, tests/

**使用方法：**
```bash
# 启用 AI 智能分析（需要设置 API Key）
export DEEPSEEK_API_KEY=your_key
node bin/vue-migrator.js migrate --source ./old-project --target ./new-project

# 查看智能复制的详细过程
node bin/vue-migrator.js migrate --source ./old-project --target ./new-project --verbose
```

## ✨ Bonus：整合为自动化 CLI 工具

你可以封装上述流程为一个 CLI 脚本，例如使用 Node + Commander：

```bash
npx vue2to3-migrator --input ./src --ai --eslint --build
```

## 🚀 CLI 使用指南

### 依赖升级工具（新增）

PackageUpgrader 是一个智能的依赖版本升级工具，专门处理 Vue 2 到 Vue 3 迁移过程中的依赖兼容性问题。

```bash
# 升级当前项目的依赖
node bin/vue-migrator.js upgrade

# 升级指定项目的依赖
node bin/vue-migrator.js upgrade /path/to/project

# 预览模式（不实际修改文件）
node bin/vue-migrator.js upgrade --dry-run

# 显示详细信息
node bin/vue-migrator.js upgrade --verbose

# 禁用自动配置文件修复
node bin/vue-migrator.js upgrade --no-auto-fix-config

# 不保留已有的 Vue 3 依赖
node bin/vue-migrator.js upgrade --no-preserve-vue3
```

**PackageUpgrader 特性：**
- 🔍 智能检测：自动识别版本兼容性问题
- 🔧 自动修复：自动升级关键依赖到兼容版本
- 📝 配置修复：自动修改 vue.config.js 等配置文件
- 🛡️ 安全升级：保留已有的 Vue 3 兼容依赖
- 📊 详细报告：提供完整的升级结果报告

**处理的问题：**
- `htmlWebpackPlugin.getHooks is not a function` 错误
- `script-ext-html-webpack-plugin` 不兼容问题
- Vue 2 到 Vue 3 的依赖版本升级
- 配置文件自动清理

### 构建错误修复工具

BuildFixer 是一个独立的构建错误自动修复工具，可以智能识别和修复 Vue 项目的构建错误。

```bash
# 修复当前项目的构建错误
build-fixer fix

# 修复指定项目的构建错误
build-fixer fix /path/to/project

# 预览模式（不实际修改文件）
build-fixer fix --dry-run

# 显示详细信息
build-fixer fix --verbose

# 使用自定义构建命令
build-fixer fix --build-command "yarn build"

# 跳过 AI 修复，只使用规则修复
build-fixer fix --skip-ai

# 初始化配置文件
build-fixer init

# 查看配置信息
build-fixer config
```

**BuildFixer 特性：**
- 🔧 自动错误修复：智能识别和修复常见的构建错误
- 🚀 Webpack Codemod 集成：自动执行 webpack v5 代码迁移
- 🤖 AI 驱动：使用 AI 技术处理复杂的错误场景
- 📊 进度跟踪：实时显示修复进度和统计信息
- ⚙️ 灵活配置：支持通过配置文件自定义行为
- 🔍 预览模式：支持 dry-run 模式预览修复操作

详见：[BuildFixer 使用指南](./docs/build-fixer.md)

### 自动迁移模式（推荐）

```bash
# 自动迁移当前目录项目
node bin/vue-migrator.js auto

# 自动迁移指定项目
node bin/vue-migrator.js auto /path/to/project

# 启用 ESLint 修复（默认禁用）
node bin/vue-migrator.js auto --eslint

# 预览模式，不实际修改
node bin/vue-migrator.js auto --dry-run

# 指定 AI API Key
node bin/vue-migrator.js auto --ai-key <your-api-key>
```

### 传统迁移模式

```bash
# 执行完整迁移（7个步骤）
node bin/vue-migrator.js migrate

# 启用 ESLint 修复（默认禁用）
node bin/vue-migrator.js migrate --eslint

# 跳过 AI 修复
node bin/vue-migrator.js migrate --skip-ai

# 跳过构建测试
node bin/vue-migrator.js migrate --skip-build
```

### 单步执行

```bash
# 执行指定步骤 (1-7)
node bin/vue-migrator.js step 1  # 升级 package.json
node bin/vue-migrator.js step 3  # 批量迁移代码
node bin/vue-migrator.js step 6  # ESLint 修复
```

### 参数说明

- `--eslint`: 启用 ESLint 自动修复（默认禁用，避免大规模代码变更）
- `--skip-ai`: 跳过 AI 修复步骤
- `--skip-sass-migration`: 跳过 Sass 语法迁移步骤
- `--skip-build`: 跳过构建测试步骤
- `--skip-dependency-check`: 跳过依赖兼容性检查
- `--dry-run`: 预览模式，不实际修改文件
- `--verbose`: 显示详细信息
- `--ai-key <key>`: 指定 AI API Key（支持 DeepSeek/GLM/OpenAI）

### 迁移步骤说明

1. **升级 package.json 依赖** - 将 Vue 相关依赖升级到 Vue 3 版本
2. **检查依赖兼容性** - 检查第三方依赖是否支持 Vue 3
3. **批量迁移代码文件** - 使用 Gogocode 转换 .vue 和 .js 文件
4. **记录失败文件** - 记录转换失败的文件供后续处理
5. **AI 修复失败文件** - 使用 AI 自动修复转换失败的文件
6. **Sass 语法迁移** - 将 @import 转换为 @use 语法（自动检测 sass-migrator）
7. **ESLint 自动修复** - 运行 ESLint 修复格式和语法问题（默认禁用）
8. **构建项目并修复错误** - 尝试构建项目并使用 AI 修复构建错误

### Sass 语法迁移

工具支持自动迁移 Sass/SCSS 文件的语法，将废弃的 `@import` 转换为现代的 `@use` 语法。

## 🔍 页面运行时验证工具

新增的页面运行时验证工具，专为 Vue 2 到 Vue 3 迁移设计，能够自动解析项目路由并验证每个页面的运行状态。

### 主要功能

- **智能路由解析**: 自动解析 Vue Router 配置，支持 Vue 2 和 Vue 3 格式
- **自动化页面验证**: 使用无头浏览器访问每个页面，捕获运行时错误
- **AI 辅助分析**: 当静态解析失败时，使用 AI 分析代码
- **详细报告生成**: 生成 Markdown 和 JSON 格式的验证报告
- **错误自动修复**: 集成现有的 AI 错误修复功能

### 使用方法

```bash
# 完整页面验证
page-validator check [project-path]

# 仅解析路由
page-validator parse-routes [project-path]

# 验证单个 URL
page-validator validate-url http://localhost:3000/about

# 启用自动修复
page-validator check --auto-fix --verbose

# 使用外部服务器
page-validator check --base-url http://localhost:8080
```

### 验证报告

工具会自动生成详细的验证报告，包括：
- 页面验证摘要（成功率、失败页面等）
- 错误分类统计
- 性能指标分析
- 智能改进建议

详细文档请参考：[页面运行时验证工具文档](docs/runtime-page-validation.md)
