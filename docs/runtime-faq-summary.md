# Vue 2 到 Vue 3 迁移完整指南 FAQ

## 📋 目录导航

- [🔍 如何快速定位问题](#-如何快速定位问题)
- [🚨 启动阶段错误](#-启动阶段错误)
- [🗺️ 路由系统迁移](#️-路由系统迁移)
- [🎯 组件系统变更](#-组件系统变更)
- [🌐 全局API迁移](#-全局api迁移)
- [🛠️ 构建配置调整](#️-构建配置调整)
- [📦 第三方库适配](#-第三方库适配)
- [🔧 开发工具配置](#-开发工具配置)

---

## 🔍 如何快速定位问题

### 常见错误信息快速索引

| 错误信息关键词 | 跳转到解决方案 |
|---------------|----------------|
| `Cannot read properties of undefined (reading 'component')` | [→ 异步组件导入问题](#异步组件导入错误) |
| `loader is not a function` | [→ defineAsyncComponent使用](#loader-不是函数错误) |
| `router.addRoutes is not a function` | [→ 路由API变更](#动态路由添加方式变更) |
| `Route paths should start with "/"` | [→ 路由路径格式](#路由路径格式错误) |
| `addEventListener` | [→ DOM操作时机](#dom元素引用问题) |
| `is not a function` (组件方法) | [→ 组件方法暴露](#组件方法无法访问) |
| `toThousandFilter` | [→ 全局过滤器迁移](#全局过滤器迁移) |
| `process is not defined` | [→ Node.js兼容性](#process对象未定义) |
| `echarts` 相关错误 | [→ 图表库升级](#echarts兼容性问题) |

---

## 🚨 启动阶段错误

### 异步组件导入错误

**错误现象：**
```bash
Cannot read properties of undefined (reading 'component')
TypeError: Cannot read properties of undefined (reading 'component')
    at eval (./src/icons/index.js:23:16)
```

**根本原因：** Vue 3 中异步组件的定义方式发生变化

**详细解决方案：**

1. **检查 src/icons/index.js 文件：**
```javascript
// ❌ Vue 2 写法可能导致问题
const req = require.context('./svg', false, /\.svg$/)
const requireAll = requireContext => requireContext.keys().map(requireContext)

// ✅ Vue 3 兼容写法
const modules = import.meta.glob('./svg/*.svg')
// 或者使用 require.context (如果支持)
```

2. **修复组件导入：**
```javascript
// ❌ 可能有问题的写法
export default {
  component: SomeComponent // undefined 导致错误
}

// ✅ 确保组件正确导入
import SomeComponent from './SomeComponent.vue'
export default {
  component: SomeComponent
}
```

3. **异步图标组件处理：**
```javascript
// src/components/SvgIcon/index.vue
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'SvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String,
      default: ''
    }
  }
  // ... 其他配置
})
```

### loader 不是函数错误

**错误现象：**
```bash
TypeError: loader is not a function
    at load (vue/runtime-core/dist/runtime-core.esm-bundler.js:2544:62)
```

**根本原因：** Vue 3 中异步组件需要使用 `defineAsyncComponent`

**详细解决方案：**

1. **路由中的异步组件：**
```javascript
// ❌ Vue 2 写法
{
  path: '/redirect/:path(.*)',
          component: () => import('@/views/redirect/index')
}

// ✅ Vue 3 写法
import { defineAsyncComponent } from 'vue'

{
  path: '/redirect/:path(.*)',
          component: defineAsyncComponent(() => import('@/views/redirect/index'))
}
```

2. **组件中的异步子组件：**
```javascript
// ❌ Vue 2 写法
export default {
  components: {
    AsyncChild: () => import('./AsyncChild.vue')
  }
}

// ✅ Vue 3 写法
import { defineAsyncComponent } from 'vue'

export default {
  components: {
    AsyncChild: defineAsyncComponent(() => import('./AsyncChild.vue'))
  }
}
```

3. **带选项的异步组件：**
```javascript
// ✅ 高级用法
import { defineAsyncComponent } from 'vue'

const AsyncComponent = defineAsyncComponent({
  loader: () => import('./AsyncComponent.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

---

## 🗺️ 路由系统迁移

### 动态路由添加方式变更

**错误现象：**
```bash
router.addRoutes is not a function
```

**根本原因：** Vue Router 4 移除了 `addRoutes` 方法

**详细解决方案：**

1. **单个路由添加：**
```javascript
// ❌ Vue Router 3
router.addRoutes(accessRoutes)

// ✅ Vue Router 4 - 方式1：逐个添加
accessRoutes.forEach(route => {
  router.addRoute(route)
})

// ✅ Vue Router 4 - 方式2：添加到父路由
accessRoutes.forEach(route => {
  router.addRoute('parentRouteName', route)
})
```

2. **权限路由完整示例：**
```javascript
// src/permission.js
import { usePermissionStore } from '@/store/modules/permission'

// ❌ Vue 2 写法
const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
router.addRoutes(accessRoutes)

// ✅ Vue 3 写法
const permissionStore = usePermissionStore()
const accessRoutes = await permissionStore.generateRoutes(roles)

// 清除旧路由（如果需要）
const removeRoutes = []
accessRoutes.forEach(route => {
  const removeRoute = router.addRoute(route)
  removeRoutes.push(removeRoute)
})

// 存储移除函数以便后续清理
permissionStore.setRemoveRoutes(removeRoutes)
```

3. **动态路由清理：**
```javascript
// 登出时清理动态路由
function resetRouter() {
  // 获取存储的移除函数
  const removeRoutes = permissionStore.removeRoutes
  removeRoutes.forEach(removeRoute => {
    removeRoute()
  })
  permissionStore.clearRemoveRoutes()
}
```

### 路由路径格式错误

**错误现象：**
```bash
Error: Route paths should start with a "/": "external-link" should be "/external-link"
```

**详细解决方案：**

1. **检查路由配置：**
```javascript
// ❌ 错误格式
const routes = [
  {
    path: 'dashboard', // 缺少前导斜杠
    component: Layout
  },
  {
    path: 'external-link', // 错误
    component: ExternalLink
  }
]

// ✅ 正确格式
const routes = [
  {
    path: '/dashboard',
    component: Layout
  },
  {
    path: '/external-link',
    component: ExternalLink
  }
]
```

2. **子路由路径规则：**
```javascript
// ✅ 父子路由正确配置
{
  path: '/system',
          component: Layout,
        children: [
  {
    path: '', // 子路由可以为空（匹配父路径）
    component: SystemIndex
  },
  {
    path: 'user', // 子路由不需要前导斜杠
    component: UserManagement
  },
  {
    path: '/independent', // 独立路径需要前导斜杠
    component: IndependentPage
  }
]
}
```

### 路由器创建方式变更

**Vue 2 vs Vue 3 对比：**

```javascript
// ❌ Vue Router 3 写法
import VueRouter from 'vue-router'
import Vue from 'vue'

Vue.use(VueRouter)

const createRouter = () => new VueRouter({
  mode: 'hash',
  routes: constantRoutes,
  scrollBehavior: () => ({ y: 0 })
})

// ✅ Vue Router 4 写法
import { createRouter, createWebHashHistory } from 'vue-router'

const createAppRouter = () => createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
})
```

---

## 🎯 组件系统变更

### DOM元素引用问题

**错误现象：**
```bash
Cannot read properties of undefined (reading 'addEventListener')
    at Proxy.mounted (ScrollPane.vue:22:24)
```

**根本原因：** Vue 3 中 DOM 渲染时机和引用方式有变化

**详细解决方案：**

1. **使用 nextTick 确保 DOM 已渲染：**
```javascript
// ❌ Vue 2 写法（可能在 Vue 3 中失效）
export default {
  mounted() {
    this.$refs.scrollPane.addEventListener('scroll', this.handleScroll)
  }
}

// ✅ Vue 3 兼容写法
import { nextTick } from 'vue'

export default {
  mounted() {
    nextTick(() => {
      if (this.$refs.scrollPane) {
        this.$refs.scrollPane.addEventListener('scroll', this.handleScroll)
      }
    })
  }
}
```

2. **Composition API 写法：**
```javascript
// ✅ 使用 Composition API
import { ref, onMounted, nextTick } from 'vue'

export default {
  setup() {
    const scrollPane = ref(null)

    const handleScroll = () => {
      // 处理滚动
    }

    onMounted(() => {
      nextTick(() => {
        if (scrollPane.value) {
          scrollPane.value.addEventListener('scroll', handleScroll)
        }
      })
    })

    return {
      scrollPane,
      handleScroll
    }
  }
}
```

3. **模板中的引用：**
```vue
<template>
  <!-- 确保 ref 名称匹配 -->
  <div ref="scrollPane" class="scroll-container">
    <!-- 内容 -->
  </div>
</template>
```

### 组件方法无法访问

**错误现象：**
```bash
_this2.$refs.scrollPane.moveToTarget is not a function
TypeError: _this2.$refs.scrollPane.moveToTarget is not a function
```

**根本原因：** Vue 3 中组件方法不再自动暴露给父组件

**详细解决方案：**

1. **子组件中暴露方法：**
```javascript
// 子组件 ScrollPane.vue
// ✅ Options API 写法
export default {
  methods: {
    moveToTarget(target) {
      // 方法实现
      console.log('Moving to target:', target)
    },
    scrollToElement(element) {
      // 另一个方法
    }
  },
  // 暴露方法给父组件
  expose: ['moveToTarget', 'scrollToElement']
}

// ✅ Composition API 写法
import { defineExpose } from 'vue'

export default {
  setup() {
    const moveToTarget = (target) => {
      // 方法实现
    }

    const scrollToElement = (element) => {
      // 方法实现
    }

    // 暴露方法
    defineExpose({
      moveToTarget,
      scrollToElement
    })

    return {
      // 内部使用的响应式数据
    }
  }
}
```

2. **<script setup> 语法：**
```vue
<!-- 子组件 ScrollPane.vue -->
<script setup>
  import { defineExpose } from 'vue'

  const moveToTarget = (target) => {
    // 方法实现
  }

  const scrollToElement = (element) => {
    // 方法实现
  }

  // 暴露方法
  defineExpose({
    moveToTarget,
    scrollToElement
  })
</script>
```

3. **父组件中调用：**
```javascript
// 父组件
export default {
  methods: {
    handleClick() {
      // 确保引用存在后再调用
      if (this.$refs.scrollPane && this.$refs.scrollPane.moveToTarget) {
        this.$refs.scrollPane.moveToTarget('some-target')
      }
    }
  }
}
```

---

## 🌐 全局API迁移

### 全局过滤器迁移

**错误现象：**
```bash
Cannot read properties of undefined (reading 'toThousandFilter')
TypeError: Cannot read properties of undefined (reading 'toThousandFilter')
```

**根本原因：** Vue 3 完全移除了过滤器功能

**详细解决方案：**

1. **创建过滤器函数文件：**
```javascript
// src/filters/index.js
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

export function dateFilter(value, format = 'YYYY-MM-DD') {
  // 日期格式化实现
  return value ? dayjs(value).format(format) : ''
}

export function currencyFilter(value, currency = '¥') {
  return currency + toThousandFilter(value)
}

// 导出所有过滤器
export default {
  toThousandFilter,
  dateFilter,
  currencyFilter
}
```

2. **在 main.js 中注册为全局属性：**
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import filters from '@/filters'

const app = createApp(App)

// 方式1：逐个注册
Object.keys(filters).forEach(key => {
  app.config.globalProperties[key] = filters[key]
})

// 方式2：作为 $filters 对象注册（兼容旧代码）
app.config.globalProperties.$filters = filters

app.mount('#app')
```

3. **模板中使用：**
```vue
<template>
  <!-- ❌ Vue 2 过滤器语法 -->
  <span>{{ price | toThousandFilter }}</span>
  <span>{{ date | dateFilter('YYYY-MM-DD HH:mm') }}</span>

  <!-- ✅ Vue 3 全局属性语法 -->
  <span>{{ toThousandFilter(price) }}</span>
  <span>{{ dateFilter(date, 'YYYY-MM-DD HH:mm') }}</span>

  <!-- ✅ 兼容写法（如果注册了 $filters） -->
  <span>{{ $filters.toThousandFilter(price) }}</span>
  <span>{{ $filters.dateFilter(date, 'YYYY-MM-DD HH:mm') }}</span>
</template>
```

4. **组合式API中使用：**
```javascript
// 组件中
import { getCurrentInstance } from 'vue'
import { toThousandFilter } from '@/filters'

export default {
  setup() {
    // 方式1：直接导入使用
    const formatPrice = (price) => toThousandFilter(price)

    // 方式2：通过全局属性使用
    const { proxy } = getCurrentInstance()
    const formatPrice2 = (price) => proxy.toThousandFilter(price)

    return {
      formatPrice,
      formatPrice2
    }
  }
}
```

### 全局实例访问问题

**错误现象：**
```bash
Cannot read properties of undefined (reading 'config')
TypeError: Cannot read properties of undefined (reading 'config')
    at clipboardSuccess (clipboard.js:9:18)
```

**详细解决方案：**

1. **在 main.js 中暴露应用实例：**
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)

// 挂载到 window 对象供全局访问
window.$vueApp = app

// 或者更安全的方式，只暴露需要的 API
window.$message = app.config.globalProperties.$message
window.$notify = app.config.globalProperties.$notify

app.mount('#app')
```

2. **工具函数中使用：**
```javascript
// src/utils/clipboard.js
import Clipboard from 'clipboard'

function clipboardSuccess() {
  // ✅ 通过全局暴露的实例访问
  if (window.$vueApp) {
    window.$vueApp.config.globalProperties.$message.success('复制成功')
  }

  // ✅ 或使用直接暴露的方法
  if (window.$message) {
    window.$message.success('复制成功')
  }
}

function clipboardError() {
  if (window.$message) {
    window.$message.error('复制失败')
  }
}

export default function handleClipboard(text, event) {
  const clipboard = new Clipboard(event.target, {
    text: () => text
  })

  clipboard.on('success', () => {
    clipboardSuccess()
    clipboard.destroy()
  })

  clipboard.on('error', () => {
    clipboardError()
    clipboard.destroy()
  })

  clipboard.onClick(event)
}
```

3. **更优雅的解决方案 - 使用依赖注入：**
```javascript
// src/composables/useMessage.js
import { inject } from 'vue'

export function useMessage() {
  // 在组件中使用
  const message = inject('$message')
  return message
}

// main.js 中提供
app.provide('$message', app.config.globalProperties.$message)
```

---

## 🛠️ 构建配置调整

### process对象未定义

**错误现象：**
```bash
ReferenceError: process is not defined
    at Object.resolve (path-browserify/index.js:124:11)
```

**根本原因：** 浏览器环境没有 Node.js 的 `process` 对象

**详细解决方案：**

1. **Vue CLI 项目配置：**
```javascript
// vue.config.js
const webpack = require('webpack')

module.exports = {
  configureWebpack: {
    plugins: [
      new webpack.ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer']
      })
    ],
    resolve: {
      fallback: {
        process: require.resolve('process/browser'),
        buffer: require.resolve('buffer'),
        path: require.resolve('path-browserify'),
        os: require.resolve('os-browserify/browser'),
        crypto: require.resolve('crypto-browserify')
      }
    }
  }
}
```

2. **Vite 项目配置：**
```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  define: {
    global: 'globalThis'
  },
  resolve: {
    alias: {
      process: 'process/browser',
      buffer: 'buffer',
      path: 'path-browserify'
    }
  },
  optimizeDeps: {
    include: ['process', 'buffer']
  }
})
```

3. **安装必要依赖：**
```bash
npm install --save-dev process buffer path-browserify os-browserify crypto-browserify
# 或
pnpm add -D process buffer path-browserify os-browserify crypto-browserify
```

### SVG图标处理配置

**问题现象：** SVG 图标不显示或 SVGO 配置错误

**详细解决方案：**

1. **正确的 webpack 配置：**
```javascript
// vue.config.js
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  chainWebpack: config => {
    // 排除 icons 目录中的 svg 文件，不让默认的 svg 规则处理
    config.module
            .rule('svg')
            .exclude.add(resolve('src/icons'))
            .end()

    // 专门处理 icons 目录中的 svg 文件
    config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
              symbolId: 'icon-[name]'
            })
            .end()

    // 如果需要优化 svg
    config.module
            .rule('icons')
            .use('svgo-loader')
            .loader('svgo-loader')
            .options({
              plugins: [
                { name: 'removeViewBox', active: false },
                { name: 'removeEmptyAttrs', active: false }
              ]
            })
  }
}
```

2. **SVGO 版本兼容性处理：**
```bash
# 如果 SVGO 3.x 有问题，降级到 2.x
npm install svgo@^2.8.0 --save-dev
```

3. **图标组件更新：**
```vue
<!-- src/components/SvgIcon/index.vue -->
<template>
  <svg :class="svgClass" v-bind="$attrs" :style="{ color: color }">
    <use :xlink:href="iconName" :href="iconName" />
  </svg>
</template>

<script>
  import { defineComponent, computed } from 'vue'

  export default defineComponent({
    name: 'SvgIcon',
    inheritAttrs: false,
    props: {
      iconClass: {
        type: String,
        required: true
      },
      className: {
        type: String,
        default: ''
      },
      color: {
        type: String,
        default: ''
      }
    },
    setup(props) {
      const iconName = computed(() => `#icon-${props.iconClass}`)
      const svgClass = computed(() => {
        if (props.className) {
          return `svg-icon ${props.className}`
        }
        return 'svg-icon'
      })

      return {
        iconName,
        svgClass
      }
    }
  })
</script>
```

---

## 📦 第三方库适配

### ECharts兼容性问题

**错误现象：**
```bash
Cannot read properties of undefined (reading 'type')
    at echarts@4.2.1/lib/processor/dataSample.js:104:20
```

**根本原因：** ECharts 4.x 版本过旧，与现代构建工具兼容性差

**详细解决方案：**

1. **升级到 ECharts 5.x：**
```bash
# 卸载旧版本
npm uninstall echarts
# 安装新版本
npm install echarts@^5.5.0

# 或者使用 pnpm
pnpm add echarts@^5.5.0
```

2. **更新导入方式：**
```javascript
// ❌ ECharts 4.x 导入方式
import echarts from 'echarts'

// ✅ ECharts 5.x 导入方式
import * as echarts from 'echarts'

// ✅ 按需导入（推荐，减小打包体积）
import { init, use } from 'echarts/core'
import { BarChart, LineChart } from 'echarts/charts'
import { GridComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([BarChart, LineChart, GridComponent, CanvasRenderer])
```

3. **组件中使用：**
```javascript
// ✅ Vue 3 + ECharts 5.x 完整示例
import { defineComponent, ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  name: 'EchartsDemo',
  setup() {
    const chartRef = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (chartRef.value) {
        chartInstance = echarts.init(chartRef.value)

        const option = {
          title: {
            text: 'ECharts 入门示例'
          },
          tooltip: {},
          xAxis: {
            data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
          },
          yAxis: {},
          series: [{
            name: '销量',
            type: 'bar',
            data: [5, 20, 36, 10, 10, 20]
          }]
        }

        chartInstance.setOption(option)
      }
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      window.removeEventListener('resize', resizeChart)
      if (chartInstance) {
        chartInstance.dispose()
      }
    })

    return {
      chartRef
    }
  }
})
```

### Element Plus 迁移

**详细解决方案：**

1. **安装 Element Plus：**
```bash
npm uninstall element-ui
npm install element-plus @element-plus/icons-vue
```

2. **更新导入方式：**
```javascript
// main.js
// ❌ Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
Vue.use(ElementUI)

// ✅ Element Plus 完整导入
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
app.use(ElementPlus)

// ✅ Element Plus 按需导入（推荐）
import { ElButton, ElSelect } from 'element-plus'
app.use(ElButton).use(ElSelect)
```

3. **图标使用更新：**
```vue
<template>
  <!-- ❌ Element UI 图标 -->
  <i class="el-icon-edit"></i>

  <!-- ✅ Element Plus 图标 -->
  <el-icon><Edit /></el-icon>
</template>

<script>
  import { Edit } from '@element-plus/icons-vue'

  export default {
    components: {
      Edit
    }
  }
</script>
```

---

## 🔧 开发工具配置

### ESLint 配置更新

```javascript
// .eslintrc.js
module.exports = {
  root: true,
  env: {
    node: true,
    'vue/setup-compiler-macros': true // 支持 Vue 3 宏
  },
  extends: [
    'plugin:vue/vue3-essential', // Vue 3 规则
    '@vue/standard'
  ],
  parserOptions: {
    ecmaVersion: 2020
  },
  rules: {
    // Vue 3 特定规则
    'vue/no-multiple-template-root': 'off', // Vue 3 支持多根节点
    'vue/no-v-model-argument': 'off', // Vue 3 支持 v-model 参数
  },
  globals: {
    defineProps: 'readonly',
    defineEmits: 'readonly',
    defineExpose: 'readonly',
    withDefaults: 'readonly'
  }
}
```

### TypeScript 支持

```typescript
// 如果使用 TypeScript
// shims-vue.d.ts
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 全局类型定义
declare global {
  interface Window {
    $vueApp: any
    $message: any
  }
}
```

---

## 🚀 完整迁移检查清单

### 必须修改项 ✅

- [ ] **路由系统**
  - [ ] `router.addRoutes()` → `router.addRoute()`
  - [ ] 路由路径添加前导斜杠
  - [ ] 异步组件使用 `defineAsyncComponent`
  - [ ] 路由创建方式更新

- [ ] **组件系统**
  - [ ] 全局过滤器改为全局属性或方法
  - [ ] 子组件方法使用 `defineExpose` 暴露
  - [ ] DOM 操作添加 `nextTick` 处理
  - [ ] 组件事件使用 `emits` 选项声明

- [ ] **全局 API**
  - [ ] Vue 实例创建方式：`new Vue()` → `createApp()`
  - [ ] 全局配置：`Vue.config` → `app.config`
  - [ ] 全局方法：`Vue.use()` → `app.use()`
  - [ ] 全局属性：`Vue.prototype` → `app.config.globalProperties`

- [ ] **第三方库升级**
  - [ ] Vue Router: 3.x → 4.x
  - [ ] Vuex: 3.x → 4.x (或迁移到 Pinia)
  - [ ] Element UI → Element Plus
  - [ ] ECharts: 4.x → 5.x
  - [ ] 其他 Vue 2 专用库查找 Vue 3 替代品

### 可能需要修改项 ⚠️

- [ ] **构建配置**
  - [ ] Webpack 5 兼容性配置
  - [ ] Node.js polyfills (process, Buffer 等)
  - [ ] SVG 处理配置更新
  - [ ] ESLint 规则更新

- [ ] **代码风格**
  - [ ] 考虑使用 Composition API
  - [ ] 考虑使用 `<script setup>` 语法糖
  - [ ] TypeScript 支持增强

---

## 🎯 迁移策略建议

### 阶段一：基础运行 (1-2天)
1. 升级核心依赖版本
2. 修复启动错误
3. 确保应用能够正常启动和基本导航

### 阶段二：功能修复 (3-5天)
1. 修复路由相关问题
2. 修复组件交互问题
3. 修复全局功能（过滤器、消息提示等）

### 阶段三：优化完善 (2-3天)
1. 性能优化
2. 代码风格统一
3. 测试覆盖
4. 文档更新

### 阶段四：现代化改造 (可选)
1. 引入 Composition API
2. 使用 `<script setup>`
3. TypeScript 迁移
4. 状态管理现代化（Pinia）

---

## 🔧 实用工具和资源

### 自动化迁移工具

1. **Vue Codemod**
```bash
# 安装 Vue 官方迁移工具
npm install -g @vue/compat-migration-build

# 运行迁移检查
vue-compat-check src/
```

2. **GoGoCode** (阿里开源)
```bash
npm install -g gogocode-cli

# 批量替换 API
gogocode -s src -t "Vue.use($_$)" -r "app.use($_$)"
```

### 官方迁移指南
- [Vue 3 迁移指南](https://v3-migration.vuejs.org/)
- [Vue Router 4 迁移指南](https://router.vuejs.org/guide/migration/)
- [Vuex 4 迁移指南](https://vuex.vuejs.org/guide/migrating-to-4-0-from-3-x.html)

### 调试工具
- Vue DevTools 6.0+ (支持 Vue 3)
- Vite Dev Server (更快的开发体验)
- Vue Language Server (Volar)

---

## 🚨 常见陷阱和注意事项

### 1. 不要混用 Vue 2 和 Vue 3 的包
```bash
# ❌ 错误：同时安装了两个版本
npm list | grep vue
├── vue@2.6.14
└── vue@3.3.4

# ✅ 正确：只保留 Vue 3
npm uninstall vue@2
```

### 2. 检查第三方组件库兼容性
```javascript
// 迁移前检查每个第三方库是否支持 Vue 3
const checkList = [
  'element-ui',    // → element-plus
  'vue-router',    // → vue-router@4
  'vuex',          // → vuex@4 或 pinia
  'vue-i18n',      // → vue-i18n@9
  'echarts',       // → echarts@5
  // ... 其他依赖
]
```

### 3. 渐进式迁移技巧
```javascript
// 使用 @vue/compat 实现渐进式迁移
// package.json
{
  "dependencies": {
    "vue": "^3.3.0",
    "@vue/compat": "^3.3.0"
  }
}

// main.js
import { createApp } from '@vue/compat'

const app = createApp(App)
app.config.compilerOptions.compatConfig = {
  MODE: 2, // Vue 2 兼容模式
  FEATURE_FLAGS: {
    COMPILER_FILTERS: false // 禁用过滤器支持，强制迁移
  }
}
```

### 4. 性能监控
```javascript
// 添加性能监控，确保迁移后性能不下降
if (process.env.NODE_ENV === 'development') {
  app.config.performance = true
  
  // 监控组件渲染时间
  app.mixin({
    beforeCreate() {
      this.$options._startTime = performance.now()
    },
    mounted() {
      const time = performance.now() - this.$options._startTime
      if (time > 100) {
        console.warn(`组件 ${this.$options.name} 挂载耗时 ${time.toFixed(2)}ms`)
      }
    }
  })
}
```

---

## 📞 获取帮助

遇到问题时的求助渠道：

1. **官方资源**
  - [Vue 3 官方文档](https://vuejs.org/)
  - [Vue 3 迁移指南](https://v3-migration.vuejs.org/)
  - [GitHub Issues](https://github.com/vuejs/core/issues)

2. **社区资源**
  - Vue 中文社区
  - Stack Overflow
  - 掘金 Vue 专栏

3. **问题排查步骤**
  1. 查看浏览器控制台完整错误信息
  2. 对照本 FAQ 寻找相似问题
  3. 检查相关依赖版本兼容性
  4. 创建最小复现示例
  5. 搜索相关 issue 或提问

---

> 💡 **温馨提示**：迁移是一个渐进的过程，不要急于一次性改完所有代码。建议先让项目跑起来，再逐步优化和现代化。保持耐心，Vue 3 带来的改进绝对值得这次迁移！

---

## 📊 迁移进度追踪模板

可以复制以下模板来追踪你的迁移进度：

```markdown
## 我的 Vue 3 迁移进度

### 🚀 启动阶段
- [ ] 依赖版本升级完成
- [ ] 应用可以正常启动
- [ ] 基本页面可以访问
- [ ] 控制台无严重错误

### 🔧 核心功能修复
- [ ] 路由系统正常工作
- [ ] 组件交互无问题  
- [ ] 全局过滤器已迁移
- [ ] 权限控制正常
- [ ] 数据请求正常

### 🎨 UI 组件修复  
- [ ] Element Plus 迁移完成
- [ ] 图标显示正常
- [ ] 表单功能正常
- [ ] 弹窗组件正常
- [ ] 数据表格正常

### 📊 图表和可视化
- [ ] ECharts 升级完成
- [ ] 图表显示正常
- [ ] 数据更新正常

### ✅ 测试验证
- [ ] 主要功能流程测试通过
- [ ] 浏览器兼容性测试
- [ ] 性能对比测试
- [ ] 用户接受度测试

### 🚀 上线准备
- [ ] 生产环境构建成功
- [ ] 部署流程验证
- [ ] 回滚方案准备
- [ ] 监控告警配置
```
