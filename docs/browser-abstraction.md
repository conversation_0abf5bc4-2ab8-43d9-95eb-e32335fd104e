# 浏览器自动化抽象层

本项目实现了一个浏览器自动化抽象层，允许在 Puppeteer 和 Playwright 之间无缝切换，为 Vue 2 到 Vue 3 迁移工具提供更灵活的浏览器自动化支持。

## 特性

- 🔄 **统一接口**: 为 Puppeteer 和 Playwright 提供统一的 API
- 🎯 **自动检测**: 自动检测可用的浏览器和自动化工具
- 🔧 **灵活配置**: 支持手动选择或自动选择最佳适配器
- 🛡️ **错误处理**: 优雅处理工具未安装或浏览器不可用的情况
- 📦 **可扩展**: 支持注册自定义适配器

## 架构设计

```
src/infrastructure/browser/
├── interfaces/           # 抽象接口定义
│   ├── IBrowserAutomation.js
│   ├── IBrowser.js
│   ├── IPage.js
│   ├── IElement.js
│   └── IResponse.js
├── adapters/            # 具体实现
│   ├── PuppeteerAdapter.js
│   └── PlaywrightAdapter.js
├── BrowserFactory.js    # 工厂类
├── BrowserLaunchOptions.js  # 配置选项
└── index.js            # 导出入口
```

## 快速开始

### 基本使用

```javascript
const { browserFactory } = require('./src/infrastructure/browser');

// 使用默认适配器（Puppeteer）
const adapter = browserFactory.createBrowserAutomation();

// 启动浏览器
const browser = await adapter.launch({
  headless: true,
  args: ['--no-sandbox']
});

// 创建页面
const page = await browser.newPage();
await page.goto('https://example.com');

// 关闭浏览器
await browser.close();
```

### 指定适配器类型

```javascript
// 使用 Puppeteer
const puppeteerAdapter = browserFactory.createBrowserAutomation('puppeteer');

// 使用 Playwright Chromium
const playwrightAdapter = browserFactory.createBrowserAutomation('playwright-chromium');

// 使用 Playwright Firefox
const firefoxAdapter = browserFactory.createBrowserAutomation('playwright-firefox');
```

### 自动选择最佳适配器

```javascript
const adapter = await browserFactory.createConfiguredBrowserAutomation({
  autoSelect: true,
  preferredTypes: ['playwright-chromium', 'puppeteer']
});
```

## API 参考

### BrowserFactory

#### `createBrowserAutomation(type?)`
创建浏览器自动化适配器。

- `type` (string, 可选): 适配器类型，支持：
  - `'puppeteer'` - Puppeteer 适配器
  - `'playwright-chromium'` - Playwright Chromium 适配器
  - `'playwright-firefox'` - Playwright Firefox 适配器
  - `'playwright-webkit'` - Playwright WebKit 适配器

#### `detectAvailableBrowsers()`
检测所有可用的浏览器。

返回: `Promise<Array<BrowserInfo>>`

#### `selectBestAdapter(preferredTypes?)`
选择最佳的适配器。

- `preferredTypes` (string[], 可选): 首选的适配器类型列表

#### `createConfiguredBrowserAutomation(config)`
创建配置好的浏览器自动化实例。

- `config.type` (string, 可选): 指定适配器类型
- `config.autoSelect` (boolean, 可选): 是否自动选择
- `config.preferredTypes` (string[], 可选): 首选类型列表

### IBrowserAutomation 接口

所有适配器都实现此接口：

```javascript
interface IBrowserAutomation {
  async launch(options): Promise<IBrowser>
  async detectBrowsers(): Promise<Array<BrowserInfo>>
  async downloadBrowser(): Promise<boolean>
  getBrowserType(): string
}
```

### IBrowser 接口

```javascript
interface IBrowser {
  async newPage(): Promise<IPage>
  async pages(): Promise<Array<IPage>>
  async close(): Promise<void>
  async version(): Promise<string>
}
```

### IPage 接口

```javascript
interface IPage {
  async goto(url, options?): Promise<IResponse>
  async url(): Promise<string>
  async setViewport(viewport): Promise<void>
  async screenshot(options?): Promise<Buffer>
  async evaluate(fn, ...args): Promise<any>
  async waitForSelector(selector, options?): Promise<IElement>
  async $(selector): Promise<IElement>
  async $$(selector): Promise<Array<IElement>>
  async close(): Promise<void>
}
```

## 迁移指南

### 从直接使用 Puppeteer 迁移

**之前:**
```javascript
const puppeteer = require('puppeteer');

const browser = await puppeteer.launch({ headless: true });
const page = await browser.newPage();
await page.goto('https://example.com');
await browser.close();
```

**之后:**
```javascript
const { browserFactory } = require('./src/infrastructure/browser');

const adapter = browserFactory.createBrowserAutomation('puppeteer');
const browser = await adapter.launch({ headless: true });
const page = await browser.newPage();
await page.goto('https://example.com');
await browser.close();
```

### 项目中的具体更改

1. **BrowserDetector.js**: 更新为使用抽象层检测多种浏览器
2. **PageValidator.js**: 修改为通过工厂创建浏览器实例
3. **AutoLoginManager.js**: 无需修改，继续使用页面接口

## 配置选项

### BrowserLaunchOptions

```javascript
const options = {
  headless: true,           // 无头模式
  devtools: false,          // 开发者工具
  slowMo: 0,               // 操作延迟（毫秒）
  args: ['--no-sandbox'],   // 启动参数
  executablePath: '/path/to/browser',  // 浏览器路径
  timeout: 30000           // 超时时间
};
```

## 错误处理

抽象层提供了优雅的错误处理：

```javascript
try {
  const adapter = browserFactory.createBrowserAutomation('playwright-chromium');
  // 使用适配器...
} catch (error) {
  if (error.message.includes('Playwright is not installed')) {
    console.log('请安装 Playwright: npm install playwright');
    // 回退到 Puppeteer
    const adapter = browserFactory.createBrowserAutomation('puppeteer');
  }
}
```

## 扩展性

### 注册自定义适配器

```javascript
class CustomAdapter extends IBrowserAutomation {
  getBrowserType() {
    return 'custom';
  }
  
  async launch(options) {
    // 自定义实现
  }
  
  // 实现其他必需方法...
}

browserFactory.registerAdapter('custom', CustomAdapter);
```

## 测试

运行浏览器抽象层测试：

```bash
npm test -- test/infrastructure/browser/
```

## 示例

查看完整的使用示例：

```bash
node examples/browser-abstraction-usage.js
```

## 依赖

- **必需**: `puppeteer` (默认适配器)
- **可选**: `playwright` (用于 Playwright 适配器)

安装 Playwright:
```bash
npm install playwright
```

## 贡献

欢迎贡献新的适配器实现或改进现有功能。请确保：

1. 实现所有必需的接口方法
2. 添加相应的单元测试
3. 更新文档

## 许可证

MIT License
