# FAQ

### ICON

```
Uncaught runtime errors:
×
ERROR
Cannot read properties of undefined (reading 'component')
TypeError: Cannot read properties of undefined (reading 'component')
    at eval (webpack-internal:///./src/icons/index.js:23:16)
    at ./src/icons/index.js (http://localhost:9527/static/js/app.js:6317:1)
    at __webpack_require__ (http://localhost:9527/static/js/runtime.js:34:32)
    at fn (http://localhost:9527/static/js/runtime.js:275:21)
    at eval (webpack-internal:///./src/main.js:34:65)
    at ./src/main.js (http://localhost:9527/static/js/app.js:7516:1)
    at __webpack_require__ (http://localhost:9527/static/js/runtime.js:34:32)
    at __webpack_exec__ (http://localhost:9527/static/js/app.js:11978:61)
    at http://localhost:9527/static/js/app.js:11979:591
    at __webpack_require__.O (http://localhost:9527/static/js/runtime.js:79:23)
```


### TypeError: loader is not a function

```bash
ERROR
loader is not a function
TypeError: loader is not a function
    at load (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:2544:62)
    at setup (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:2651:7)
    at callWithErrorHandling (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:351:19)
    at setupStatefulComponent (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:8086:25)
    at setupComponent (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:8047:36)
    at mountComponent (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:5369:7)
    at processComponent (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:5335:9)
    at patch (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:4863:11)
    at ReactiveEffect.componentUpdateFn [as fn] (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:5560:9)
    at ReactiveEffect.run (webpack-internal:///./node_modules/.pnpm/@vue+reactivity@3.5.17/node_modules/@vue/reactivity/dist/reactivity.esm-bundler.js:291:19)
```


删除多余的：

```bash
const createRouter = () =>
  VueRouter.createRouter({
    routes: constantRoutes,
    history: VueRouter.createWebHashHistory(),
    scrollBehavior: () => ({
      top: 0,
    }),
  })
```

重新编写路由：

```javascript
export const constantRoutes = [
	{
		path: '/redirect',
		component: Layout,
		hidden: true,
		children: [
			{
				path: '/redirect/:path(.*)',
				component: Vue.defineAsyncComponent(
					Vue.defineAsyncComponent(() => import('@/views/redirect/index'))
				),
			},
		],
	}
} 
```


## 权限路由 `permissions.js`

用 gogocode 检查 `router.addRoutes(accessRoutes)`

```
// 旧版 (Vue Router 3)
router.addRoutes(accessRoutes)

// 新版 (Vue Router 4)
accessRoutes.forEach(route => {
  router.addRoute(route)
})
```


## 路由检查 2

```bash
Error: Route paths should start with a "/": "external-link" should be "/external-link".
    at eval (permission.js:47:1)
    at Array.forEach (<anonymous>)
    at eval (permission.js:46:1)
```

## 用法检查

```
ERROR
Cannot read properties of undefined (reading 'addEventListener')
TypeError: Cannot read properties of undefined (reading 'addEventListener')
    at Proxy.mounted (webpack-internal:///./node_modules/.pnpm/babel-loader@8.4.1_@babel+core@7.27.4_webpack@5.99.9/node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/layout/components/TagsView/ScrollPane.vue?vue&type=script&lang=js:22:24)
  
```


这个错误：

```
Cannot read properties of undefined (reading 'addEventListener')
```

## vue-count-to

```
Uncaught runtime errors:
×
ERROR
Cannot read properties of undefined (reading 'toThousandFilter')
TypeError: Cannot read properties of undefined (reading 'toThousandFilter')
    at eval (webpack-internal:///./node_modules/.pnpm/babel-loader@8.4.1_@babel+core@7.27.4_webpack@5.99.9/node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/templateLoader.js??ruleSet[1].rules[3]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/views/dashboard/admin/components/TransactionTable.vue?vue&type=template&id=4d25bee4:33:146)
    at Proxy.renderFnWithContext (webpack-internal:///./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.esm-bundler.js:841:13)
    at column.renderCell (webpack-internal:///./node_modules/.pnpm/element-plus@2.10.2_vue@3.5.17/node_modules/element-plus/es/components/table/src/table-column/render-helper.mjs:128:39)
    at cellChildren (webpack-internal:///./node_modules/.pnpm/element-plus@2.10.2_vue@3.5.17/node_modules/element-plus/es/components/table/src/table-body/render-helper.mjs:125:19)
```


## Cannot read properties of undefined (reading 'toThousandFilter')

```vue2
¥{{ toThousandFilter(scope.row.price) }}
```

在 main.js 改为使用这种方式：

```vue3
Object.keys(filters).forEach((key) => {
  app.config.globalProperties[key] = filters[key]
})

// 兼容旧的
app.config.globalProperties.$filters = filters
```


## _this2.$refs.scrollPane.moveToTarget is not a function

问题：但 Vue 3 中，组件方法不再默认暴露给 ref 引用，需要你显式地暴露方法给父组件！

✅ 解决方案：使用 defineExpose 显式暴露方法


```bash
ERROR
_this2.$refs.scrollPane.moveToTarget is not a function
TypeError: _this2.$refs.scrollPane.moveToTarget is not a function
    at Proxy.eval (webpack-internal:///./node_modules/.pnpm/babel-loader@8.4.1_@babel+core@7.27.4_webpack@5.99.9/node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/layout/components/TagsView/index.vue?vue&type=script&lang=js:149:39)
```

## SVGO 降级


"svgo": "^3.0.5",

```bash
> svgo -f src/icons/svg --config=src/icons/svgo.yml

TypeError [ERR_UNKNOWN_FILE_EXTENSION]: Unknown file extension ".yml" for /Users/<USER>/works/galaxy/galaxy-vue3-demi/src/icons/svgo.yml
    at Object.getFileProtocolModuleFormat [as file:] (node:internal/modules/esm/get_format:219:9)
    at defaultGetFormat (node:internal/modules/esm/get_format:245:36)
    at defaultLoad (node:internal/modules/esm/load:120:22)
    at async ModuleLoader.loadAndTranslate (node:internal/modules/esm/loader:580:32)
    at async ModuleJob._link (node:internal/modules/esm/module_job:116:19)
 ELIFECYCLE  Command failed with exit code 1.
```

图标不显示：

```javascript
  chainWebpack: config => {
	/// others

	// 排除 icons 目录中的 svg 文件，使其不被默认的 svg 规则处理
	config.module
		.rule('svg')
		.exclude.add(resolve('src/icons'))
		.end()

	// 专门处理 icons 目录中的 svg 文件
	////
}
```

## Cannot read properties of undefined (reading 'config')

```bash
Uncaught runtime errors:
×
ERROR
Cannot read properties of undefined (reading 'config')
TypeError: Cannot read properties of undefined (reading 'config')
    at clipboardSuccess (webpack-internal:///./src/utils/clipboard.js:9:18)
    at eval (webpack-internal:///./src/utils/clipboard.js:28:5)
    at Clipboard.emit (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:660:20)
    at ClipboardAction.handleResult (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:473:26)
    at ClipboardAction.copyText (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:462:18)
    at ClipboardAction.selectFake (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:413:18)
    at ClipboardAction.initSelection (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:368:22)
    at new ClipboardAction (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:335:14)
    at Clipboard.onClick (webpack-internal:///./node_modules/.pnpm/clipboard@2.0.4/node_modules/clipboard/dist/clipboard.js:198:36)
    at handleClipboard (webpack-internal:///./src/utils/clipboard.js:35:13)
```

在 main.js 中添加：

```javascript
window.$vueApp = app
```

## Process ??

```bash
Uncaught runtime errors:
×
ERROR
process is not defined
ReferenceError: process is not defined
    at Object.resolve (webpack-internal:///./node_modules/.pnpm/path-browserify@1.0.1/node_modules/path-browserify/index.js:124:11)
    at Proxy.onlyOneShowingChild (webpack-internal:///./node_modules/.pnpm/babel-loader@8.4.1_@babel+core@7.27.4_webpack@5.99.9/node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/views/permission/role.vue?vue&type=script&lang=js:325:74)
    at Proxy.generateRoutes (webpack-internal:///./node_modules/.pnpm/babel-loader@8.4.1_@babel+core@7.27.4_webpack@5.99.9/node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/views/permission/role.vue?vue&type=script&lang=js:143:42)
    at eval (webpack-internal:///./node_modules/.pnpm/babel-loader@8.4.1_@babel+core@7.27.4_webpack@5.99.9/node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[0]!./node_modules/.pnpm/vue-loader@17.4.2_@vue+compiler-sfc@3.5.17_vue@3.5.17_webpack@5.99.9/node_modules/vue-loader/dist/index.js??ruleSet[0].use[0]!./src/views/permission/role.vue?vue&type=script&lang=js:105:36)
    at Generator.eval (webpack-internal:///./node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/regenerator.js:71:51)
    at Generator.eval [as next] (webpack-internal:///./node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/regeneratorDefine.js:21:23)
    at asyncGeneratorStep (webpack-internal:///./node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js:9:17)
    at _next (webpack-internal:///./node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js:23:9)
```

```bash
const webpack = require('webpack')

module.exports = {
  configureWebpack: {
    plugins: [
      new webpack.ProvidePlugin({
        process: 'process/browser', // 提供一个浏览器兼容的 process
      }),
    ],
    resolve: {
      fallback: {
        process: require.resolve('process/browser'),
      },
    },
  },
}

```

## Echarts

这个报错：

```
Cannot read properties of undefined (reading 'type')
```

出现在：

```
echarts@4.2.1/lib/processor/dataSample.js:104:20
```

### 原因分析

ECharts 4.2.1 在执行 `dataSample.js` 的 `reset()` 函数时，尝试读取某个对象的 `type` 属性，但是这个对象是 `undefined`，说明：

* 配置中某个 series 的字段不符合预期，或者
* 某个中间环节的数据被破坏了，或格式不匹配。

结合你提供的配置代码和使用的版本：

#### ⚠️ **ECharts 4.2.1 版本问题：**

* `ECharts 4.2.1` 属于较早版本，**兼容性差**、**内部 Bug 较多**，尤其是在组合 `theme` + `bar/stack` + `animationDuration` 等特性时，容易触发未知行为。
* ECharts 团队建议使用至少 `5.x` 版本。

---

### ✅ 建议解决方案

#### ✅ 1. **升级 ECharts 到最新版**

推荐版本：`^5.5.0`（最新为 `5.5.x`）

```bash
pnpm add echarts@^5.5.0
# or
npm install echarts@^5.5.0
```

然后将 `import echarts from 'echarts'` 改为：

```js
import * as echarts from 'echarts'
```

ECharts 5.x 使用了 ESModule 风格，不再默认导出。

