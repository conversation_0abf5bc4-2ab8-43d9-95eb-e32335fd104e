#!/usr/bin/env node

require('dotenv').config()

const { Command } = require('commander')
const chalk = require('chalk')
const path = require('path')
const fs = require('fs-extra')
const VueMigrator = require('../src/app/VueMigrator')
const program = new Command()

program
	.name('vue-migrator')
	.description('Vue 2 到 Vue 3 统一迁移工具')
	.version('1.0.0')

program
	.command('migrate <project-path>')
	.description('🔄 将 Vue 2 项目代码迁移到 Vue 3（专注于代码转换）')
	.option('--skip-dependency-check', '跳过依赖兼容性检查')
	.option('--skip-ai', '跳过 AI 修复步骤')
	.option('--eslint', '启用 ESLint 自动修复（默认禁用）')
	.option('--skip-build', '跳过构建和构建错误修复')
	.option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
	.option('--build-command <cmd>', '构建命令', 'npm run build')
	.option('--dry-run', '预览模式，不实际修改文件')
	.option('--verbose', '显示详细信息')
	.option('--quiet', '静默模式，仅显示关键信息')
	.action(async (projectPath, options) => {
		try {
			const resolvedProjectPath = path.resolve(projectPath)

			if (!await fs.pathExists(resolvedProjectPath)) {
				throw new Error(`项目路径不存在: ${resolvedProjectPath}`)
			}

			console.log(chalk.blue('🚀 开始 Vue 2 到 Vue 3 代码迁移...'))
			const migrator = new VueMigrator(resolvedProjectPath, {
				isOutputMode: false,
				srcDir: 'src',
				outputSrcDir: 'src',
				buildCommand: options.buildCommand,
				dryRun: options.dryRun ? options.dryRun : false,
				verbose: options.verbose ? options.verbose : false,
				quiet: options.quiet ? options.quiet : false
			})

			const result = await migrator.migrate()

			if (result.success) {
				console.log(chalk.bold.green('\n✅ Vue 代码迁移完成！'))
				if (result.failedFiles && result.failedFiles.length > 0) {
					console.log(chalk.yellow(`\n⚠️  发现 ${result.failedFiles.length} 个转换失败的文件，可能需要手动修复`))

					if (!options.quiet) {
						const recommendations = migrator.generateRecommendations()
						console.log(chalk.yellow('\n💡 后续建议:'))
						recommendations.forEach((rec, index) => {
							console.log(`${index + 1}. ${rec}`)
						})
					}
				} else {
					if (!options.quiet) {
						console.log(chalk.yellow('\n💡 后续建议:'))
						console.log('1. 检查迁移后的代码是否正常工作')
						console.log('2. 运行测试确保功能正常')
					}
				}
			} else {
				console.log(chalk.red('\n❌ Vue 代码迁移失败'))
			}

		} catch (error) {
			console.error(chalk.red('\n❌ 迁移失败:'), error.message);
			if (process.env.DEBUG) {
				console.error(error.stack);
			}
			process.exit(1);
		}
	})

process.on('uncaughtException', (error) => {
	console.error(chalk.red('❌ 未捕获的异常:'), error.message)
	if (process.env.DEBUG) {
		console.error(error.stack)
	}
	process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
	console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason)
	if (process.env.DEBUG) {
		console.error(promise)
	}
	process.exit(1)
})

// 解析命令行参数
program.parse()

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
	program.outputHelp()
}
