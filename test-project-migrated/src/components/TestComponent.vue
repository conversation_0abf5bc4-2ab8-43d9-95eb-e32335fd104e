<template>
  <div class="test-component">
    <el-button @click="handleClick">Click me</el-button>
    <count-to :start-val="0" :end-val="100" :duration="3000"></count-to>
    <draggable v-model:value="list" @start="drag = true" @end="drag = false">
      <div v-for="element in list" :key="element.id">
        {{ element.name }}
      </div>
    </draggable>
  </div>
</template>

<script>
import countTo from 'vue-count-to'
import draggable from 'vuedraggable'

export default {
  name: 'TestComponent',
  components: {
    countTo,
    draggable,
  },
  data() {
    return {
      drag: false,
      list: [
        { id: 1, name: 'Item 1' },
        { id: 2, name: 'Item 2' },
      ],
    }
  },
  methods: {
    handleClick() {
      this.$message.success('Button clicked!')
    },
  },
}
</script>

<style scoped>
.test-component {
  padding: 20px;
}
</style>
