const state = {
  sidebar: {
    opened: true,
    withoutAnimation: false,
  },
  device: 'desktop',
  theme: 'light',
  language: 'en',
  size: 'medium',
}

const mutations = {
  TOGGLE_SIDEBAR: (state) => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_THEME: (state, theme) => {
    state.theme = theme
  },
  SET_LANGUAGE: (state, language) => {
    state.language = language
  },
  SET_SIZE: (state, size) => {
    state.size = size
  },
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme)
  },
  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
}

const getters = {
  sidebar: (state) => state.sidebar,
  device: (state) => state.device,
  theme: (state) => state.theme,
  language: (state) => state.language,
  size: (state) => state.size,
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
}
