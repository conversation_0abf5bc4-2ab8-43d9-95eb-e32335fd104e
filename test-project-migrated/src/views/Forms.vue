<template>
  <div class="forms">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Basic Form</span>
            </div>
          </template>
          <el-form
            :model="basicForm"
            :rules="basicRules"
            ref="basicForm"
            label-width="100px"
          >
            <el-form-item label="Name" prop="name">
              <el-input
                v-model="basicForm.name"
                placeholder="Enter your name"
              ></el-input>
            </el-form-item>
            <el-form-item label="Email" prop="email">
              <el-input
                v-model="basicForm.email"
                placeholder="Enter your email"
              ></el-input>
            </el-form-item>
            <el-form-item label="Phone" prop="phone">
              <el-input
                v-model="basicForm.phone"
                placeholder="Enter your phone"
              ></el-input>
            </el-form-item>
            <el-form-item label="Gender" prop="gender">
              <el-radio-group v-model="basicForm.gender">
                <el-radio label="male">Male</el-radio>
                <el-radio label="female">Female</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="Interests" prop="interests">
              <el-checkbox-group v-model="basicForm.interests">
                <el-checkbox label="reading">Reading</el-checkbox>
                <el-checkbox label="music">Music</el-checkbox>
                <el-checkbox label="sports">Sports</el-checkbox>
                <el-checkbox label="travel">Travel</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="Country" prop="country">
              <el-select
                v-model="basicForm.country"
                placeholder="Select country"
              >
                <el-option label="USA" value="usa"></el-option>
                <el-option label="China" value="china"></el-option>
                <el-option label="Japan" value="japan"></el-option>
                <el-option label="UK" value="uk"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="Description" prop="description">
              <el-input
                type="textarea"
                v-model="basicForm.description"
                rows="3"
                placeholder="Enter description"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitBasicForm"
                >Submit</el-button
              >
              <el-button @click="resetBasicForm">Reset</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template v-slot:header>
            <div>
              <span>Advanced Form</span>
            </div>
          </template>
          <el-form
            :model="advancedForm"
            :rules="advancedRules"
            ref="advancedForm"
            label-width="120px"
          >
            <el-form-item label="Date Range" prop="dateRange">
              <el-date-picker
                v-model="advancedForm.dateRange"
                type="daterange"
                range-separator="To"
                start-placeholder="Start date"
                end-placeholder="End date"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="Time" prop="time">
              <el-time-picker
                v-model="advancedForm.time"
                placeholder="Select time"
              >
              </el-time-picker>
            </el-form-item>
            <el-form-item label="Color" prop="color">
              <el-color-picker v-model="advancedForm.color"></el-color-picker>
            </el-form-item>
            <el-form-item label="Rating" prop="rating">
              <el-rate v-model="advancedForm.rating"></el-rate>
            </el-form-item>
            <el-form-item label="Slider" prop="slider">
              <el-slider
                v-model="advancedForm.slider"
                :min="0"
                :max="100"
              ></el-slider>
            </el-form-item>
            <el-form-item label="Switch" prop="switch">
              <el-switch v-model="advancedForm.switch"></el-switch>
            </el-form-item>
            <el-form-item label="Upload" prop="upload">
              <el-upload
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :file-list="fileList"
              >
                <el-button size="small" type="primary"
                  >Click to upload</el-button
                >
                <template v-slot:tip>
                  <div class="el-upload__tip">
                    jpg/png files with a size less than 500kb
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitAdvancedForm"
                >Submit</el-button
              >
              <el-button @click="resetAdvancedForm">Reset</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Forms',
  data() {
    return {
      basicForm: {
        name: '',
        email: '',
        phone: '',
        gender: '',
        interests: [],
        country: '',
        description: '',
      },
      basicRules: {
        name: [
          {
            required: true,
            message: 'Please enter your name',
            trigger: 'blur',
          },
          {
            min: 2,
            max: 20,
            message: 'Length should be 2 to 20 characters',
            trigger: 'blur',
          },
        ],
        email: [
          {
            required: true,
            message: 'Please enter your email',
            trigger: 'blur',
          },
          {
            type: 'email',
            message: 'Please enter a valid email address',
            trigger: 'blur',
          },
        ],
        phone: [
          {
            required: true,
            message: 'Please enter your phone number',
            trigger: 'blur',
          },
        ],
        gender: [
          {
            required: true,
            message: 'Please select your gender',
            trigger: 'change',
          },
        ],
        country: [
          {
            required: true,
            message: 'Please select your country',
            trigger: 'change',
          },
        ],
      },
      advancedForm: {
        dateRange: '',
        time: '',
        color: '#409EFF',
        rating: 0,
        slider: 50,
        switch: false,
      },
      advancedRules: {
        dateRange: [
          {
            required: true,
            message: 'Please select date range',
            trigger: 'change',
          },
        ],
        time: [
          { required: true, message: 'Please select time', trigger: 'change' },
        ],
      },
      fileList: [],
    }
  },
  methods: {
    submitBasicForm() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          this.$message.success('Basic form submitted successfully!')
          console.log('Basic form data:', this.basicForm)
        } else {
          this.$message.error('Please check your input!')
        }
      })
    },
    resetBasicForm() {
      this.$refs.basicForm.resetFields()
    },
    submitAdvancedForm() {
      this.$refs.advancedForm.validate((valid) => {
        if (valid) {
          this.$message.success('Advanced form submitted successfully!')
          console.log('Advanced form data:', this.advancedForm)
        } else {
          this.$message.error('Please check your input!')
        }
      })
    },
    resetAdvancedForm() {
      this.$refs.advancedForm.resetFields()
    },
    handleFileChange(file, fileList) {
      this.fileList = fileList
    },
  },
}
</script>

<style scoped>
.forms {
  padding: 20px;
}
</style>
