const chalk = require('chalk');
const { contextManager } = require('./ContextManager');

/**
 * ContextAwareComponent - 上下文感知组件基类
 * 
 * 为所有迁移组件提供统一的上下文访问和管理能力
 * 组件可以继承此类来获得上下文感知能力
 */
class ContextAwareComponent {
  constructor(name, options = {}) {
    this.name = name;
    this.options = options;
    this.contextId = options.contextId || null;
    this.context = null;
    this.isInitialized = false;
    this.eventSubscriptions = [];
    
    // 组件状态
    this.state = {
      status: 'idle', // idle, running, completed, failed
      startTime: null,
      endTime: null,
      result: null,
      error: null
    };
  }

  /**
   * 初始化组件
   */
  async initialize(contextId = null) {
    if (this.isInitialized) {
      return this;
    }

    try {
      // 获取或创建上下文
      if (contextId) {
        this.contextId = contextId;
        this.context = contextManager.getContext(contextId);
      } else if (this.contextId) {
        this.context = contextManager.getContext(this.contextId);
      } else {
        throw new Error(`组件 ${this.name} 需要上下文ID`);
      }

      // 注册组件到上下文
      this.context.registerTool(this.name, this);

      // 设置事件监听
      this.setupEventListeners();

      // 执行组件特定的初始化
      await this.onInitialize();

      this.isInitialized = true;
      this.logInfo('组件已初始化');

      return this;
    } catch (error) {
      this.logError('组件初始化失败', error);
      throw error;
    }
  }

  /**
   * 组件特定的初始化逻辑 - 子类可重写
   */
  async onInitialize() {
    // 子类可以重写此方法
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 监听上下文事件
    this.subscribe('context:updated', (data) => {
      this.onContextUpdated(data);
    });

    this.subscribe('migration:complete', (data) => {
      this.onMigrationComplete(data);
    });

    // 子类可以重写此方法来添加更多监听器
    this.onSetupEventListeners();
  }

  /**
   * 子类可重写的事件监听设置
   */
  onSetupEventListeners() {
    // 子类可以重写此方法
  }

  /**
   * 订阅事件
   */
  subscribe(eventType, callback) {
    const unsubscribe = contextManager.subscribe(eventType, callback, this.contextId);
    this.eventSubscriptions.push(unsubscribe);
    return unsubscribe;
  }

  /**
   * 发布事件
   */
  publish(eventType, data) {
    contextManager.publish(eventType, { component: this.name, ...data }, this.contextId);
  }

  /**
   * 获取上下文
   */
  getContext() {
    if (!this.context) {
      throw new Error(`组件 ${this.name} 未初始化或缺少上下文`);
    }
    return this.context;
  }

  /**
   * 获取项目路径
   */
  getProjectPath() {
    return this.getContext().project.path;
  }

  /**
   * 获取工作路径
   */
  getWorkingPath() {
    return this.getContext().getWorkingPath();
  }

  /**
   * 获取配置
   */
  getConfig(key = null) {
    return this.getContext().getConfig(key);
  }

  /**
   * 设置配置
   */
  setConfig(key, value) {
    this.getContext().setConfig(key, value);
    return this;
  }

  /**
   * 开始执行
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error(`组件 ${this.name} 未初始化`);
    }

    this.state.status = 'running';
    this.state.startTime = Date.now();
    this.state.error = null;

    this.logInfo('开始执行');
    this.publish('component:start', { name: this.name });

    try {
      // 执行前置检查
      await this.beforeExecute();

      // 执行主要逻辑
      this.state.result = await this.execute();

      // 执行后置处理
      await this.afterExecute();

      this.state.status = 'completed';
      this.state.endTime = Date.now();

      this.logSuccess('执行完成');
      this.publish('component:complete', { 
        name: this.name, 
        result: this.state.result,
        duration: this.getDuration()
      });

      return this.state.result;
    } catch (error) {
      this.state.status = 'failed';
      this.state.endTime = Date.now();
      this.state.error = error;

      this.logError('执行失败', error);
      this.publish('component:failed', { 
        name: this.name, 
        error: error.message,
        duration: this.getDuration()
      });

      throw error;
    }
  }

  /**
   * 执行前置检查 - 子类可重写
   */
  async beforeExecute() {
    // 子类可以重写此方法
  }

  /**
   * 主要执行逻辑 - 子类必须实现
   */
  async execute() {
    throw new Error(`组件 ${this.name} 必须实现 execute() 方法`);
  }

  /**
   * 执行后置处理 - 子类可重写
   */
  async afterExecute() {
    // 子类可以重写此方法
  }

  /**
   * 获取执行时长
   */
  getDuration() {
    if (!this.state.startTime) return 0;
    const endTime = this.state.endTime || Date.now();
    return endTime - this.state.startTime;
  }

  /**
   * 获取组件状态
   */
  getStatus() {
    return {
      name: this.name,
      status: this.state.status,
      startTime: this.state.startTime,
      endTime: this.state.endTime,
      duration: this.getDuration(),
      result: this.state.result,
      error: this.state.error
    };
  }

  /**
   * 添加错误到上下文
   */
  addError(error, context = null) {
    this.getContext().addError(error, context || this.name);
    return this;
  }

  /**
   * 添加警告到上下文
   */
  addWarning(warning, context = null) {
    this.getContext().addWarning(warning, context || this.name);
    return this;
  }

  /**
   * 更新文件状态
   */
  updateFileStatus(filePath, status) {
    this.getContext().updateFileStatus(filePath, status);
    return this;
  }

  /**
   * 记录 AI 调用
   */
  recordAICall(callInfo) {
    this.getContext().recordAICall(callInfo);
    return this;
  }

  /**
   * 上下文更新事件处理
   */
  onContextUpdated(data) {
    // 子类可以重写此方法来响应上下文更新
  }

  /**
   * 迁移完成事件处理
   */
  onMigrationComplete(data) {
    // 子类可以重写此方法来响应迁移完成
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 取消所有事件订阅
    this.eventSubscriptions.forEach(unsubscribe => unsubscribe());
    this.eventSubscriptions = [];

    // 执行组件特定的清理
    this.onCleanup();

    this.logInfo('组件已清理');
  }

  /**
   * 组件特定的清理逻辑 - 子类可重写
   */
  onCleanup() {
    // 子类可以重写此方法
  }

  /**
   * 日志方法
   */
  logInfo(message, data = null) {
    console.log(chalk.blue(`[${this.name}] ${message}`));
    if (data && this.options.verbose) {
      console.log(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  logSuccess(message, data = null) {
    console.log(chalk.green(`[${this.name}] ✅ ${message}`));
    if (data && this.options.verbose) {
      console.log(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  logWarning(message, data = null) {
    console.log(chalk.yellow(`[${this.name}] ⚠️  ${message}`));
    if (data && this.options.verbose) {
      console.log(chalk.gray(JSON.stringify(data, null, 2)));
    }
  }

  logError(message, error = null) {
    console.log(chalk.red(`[${this.name}] ❌ ${message}`));
    if (error) {
      console.log(chalk.red(`   错误: ${error.message}`));
      if (this.options.verbose && error.stack) {
        console.log(chalk.gray(error.stack));
      }
    }
  }

  /**
   * 验证组件依赖
   */
  validateDependencies(dependencies = []) {
    const context = this.getContext();
    const missing = [];

    for (const dep of dependencies) {
      if (!context.getTool(dep)) {
        missing.push(dep);
      }
    }

    if (missing.length > 0) {
      throw new Error(`组件 ${this.name} 缺少依赖: ${missing.join(', ')}`);
    }

    return true;
  }

  /**
   * 等待依赖组件完成
   */
  async waitForDependencies(dependencies = [], timeout = 30000) {
    const context = this.getContext();
    const startTime = Date.now();

    for (const dep of dependencies) {
      while (Date.now() - startTime < timeout) {
        const tool = context.getTool(dep);
        if (tool && tool.state.status === 'completed') {
          break;
        }
        
        if (tool && tool.state.status === 'failed') {
          throw new Error(`依赖组件 ${dep} 执行失败`);
        }

        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return true;
  }
}

module.exports = ContextAwareComponent;
