const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn, exec } = require('child_process');
const RuntimeErrorHandler = require('../runtime-inject/RuntimeErrorHandler');
const BrowserDetector = require('./BrowserDetector');
const BuildFixAgent = require('../build-fix/ai/BuildFixAgent');
const BuildErrorAnalyzer = require('../build-fix/ai/BuildErrorAnalyzer');
const AutoLoginManager = require('./AutoLoginManager');
const FAQHelper = require('./FAQHelper');
const ErrorAnalyzer = require('./ErrorAnalyzer');
const { browserFactory } = require('../../infrastructure/browser');

/**
 * PageValidator - 页面运行时验证器
 *
 * 功能：
 * 1. 启动开发服务器
 * 2. 使用 Puppeteer 访问每个路由页面
 * 3. 捕获页面错误和控制台输出
 * 4. 集成 RuntimeErrorHandler 进行错误处理
 * 5. 生成验证报告
 */
class PageValidator {
  constructor(projectPath, routes, options = {}) {
    this.projectPath = projectPath;
    this.allRoutes = routes || [];
    this.routeParser = options.routeParser || null; // 路由解析器实例

    // 过滤路由（如果指定了特定路由）
    if (options.specificRoutes && options.specificRoutes.length > 0) {
      this.routes = this.allRoutes.filter(route =>
        options.specificRoutes.includes(route.path)
      );
      console.log(chalk.yellow(`🎯 只验证指定的 ${this.routes.length} 个路由: ${options.specificRoutes.join(', ')}`));
    } else {
      this.routes = this.allRoutes;
    }
    this.options = {
      port: 3000,
      timeout: 30000,
      headless: 'new', // 使用新的 headless 模式
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      autoFix: false,
      maxFixAttempts: 3, // 最大修复尝试次数
      revalidateAfterFix: true, // 修复后重新验证页面
      dryRun: false, // 预览模式，不实际修改文件
      waitForServer: 60000,  // 等待服务器启动时间
      pageTimeout: 15000,   // 增加页面超时时间到15秒
      navigationTimeout: 20000, // 导航超时时间
      executablePath: null, // 浏览器可执行文件路径
      routerMode: 'hash', // Vue Router模式: 'hash' 或 'history'
      loginCredentials: {   // 登录凭据
        username: 'admin',
        password: '111111'
      },
      skipLogin: false,     // 是否跳过自动登录
      ...options
    };

    this.baseUrl = this.options.baseUrl || `http://localhost:${this.options.port}`;
    this.devServer = null;
    this.browser = null;
    this.browserAutomation = null;
    this.validationResults = [];
    this.errors = [];
    this.isLoggedIn = false; // 登录状态标记

    // 浏览器检测器
    this.browserDetector = new BrowserDetector({
      verbose: this.options.verbose,
      preferredBrowsers: ['chrome', 'chromium', 'edge']
    });

    // 集成运行时错误处理器
    if (this.options.autoFix) {
      this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
        port: this.options.port,
        autoFix: true,
        verbose: this.options.verbose
      });
    }

    // 集成 BuildFixAgent 用于页面错误修复
    if (this.options.autoFix) {
      this.buildFixAgent = new BuildFixAgent(projectPath, {
        maxAttempts: this.options.maxFixAttempts || 3,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun || false
      });
    }

    // 初始化自动登录管理器
    this.autoLoginManager = new AutoLoginManager({
      username: this.options.loginCredentials?.username || this.options.username || 'admin',
      password: this.options.loginCredentials?.password || this.options.password || '111111',
      verbose: this.options.verbose,
      aiEnabled: !this.options.skipLogin,
      configPath: path.join(projectPath, '.login-config.json')
    });

    // 初始化 FAQ 助手
    this.faqHelper = new FAQHelper(projectPath, {
      verbose: this.options.verbose
    });

    // 初始化错误分析器
    this.errorAnalyzer = new ErrorAnalyzer({
      verbose: this.options.verbose
    });

    // 初始化构建错误分析器（用于统一的错误输出处理）
    this.buildErrorAnalyzer = new BuildErrorAnalyzer(projectPath, null, null, {
      verbose: this.options.verbose
    });
  }

  /**
   * 验证所有页面 - 优化版本
   * 1. 优先验证首页 (/)
   * 2. 分批处理页面 (每批10个)
   * 3. 在登录页面也读取console信息
   */
  async validateAllPages() {
    console.log(chalk.blue(`🔍 开始验证 ${this.routes.length} 个页面...`));

    if (this.options.verbose) {
      console.log(chalk.gray(`   详细模式已启用`));
      console.log(chalk.gray(`   页面超时: ${this.options.pageTimeout}ms`));
      console.log(chalk.gray(`   导航超时: ${this.options.navigationTimeout}ms`));
      console.log(chalk.gray(`   自动修复: ${this.options.autoFix ? '启用' : '禁用'}`));
      console.log(chalk.gray(`   路由模式: ${this.options.routerMode}`));
    }

    try {
      // 0. 初始化 FAQ 系统
      await this.faqHelper.initialize();

      // 1. 启动开发服务器
      await this.startDevServer();

      // 2. 启动浏览器
      await this.startBrowser();

      // 3. 优化的页面验证流程
      await this.validatePagesOptimized();

      // 4. 生成报告
      const report = this.generateReport();

      console.log(chalk.green(`✅ 页面验证完成`));
      this.printSummary();

      return {
        success: true,
        results: this.validationResults,
        report: report,
        errors: this.errors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 页面验证失败: ${error.message}`));
      return {
        success: false,
        results: this.validationResults,
        report: null,
        errors: [...this.errors, error.message]
      };
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 优化的页面验证流程
   * 1. 优先验证首页 (/)
   * 2. 分批处理其他页面 (每批10个)
   */
  async validatePagesOptimized() {
    // 1. 首先验证首页
    const homePageResult = await this.validateHomePage();

    // 如果首页有错误且启用了自动修复，优先修复首页
    if (!homePageResult.success && this.options.autoFix) {
      console.log(chalk.yellow(`🏠 首页存在问题，优先修复...`));
      await this.fixHomePageIfNeeded(homePageResult);
    }

    // 2. 获取剩余的路由（排除首页）
    const remainingRoutes = this.routes.filter(route => route.path !== '/');

    if (remainingRoutes.length === 0) {
      console.log(chalk.green(`✅ 只有首页需要验证，验证完成`));
      return;
    }

    // 3. 分批处理剩余页面
    const batchSize = 10;
    const batches = this.createBatches(remainingRoutes, batchSize);

    console.log(chalk.blue(`📦 将验证 ${remainingRoutes.length} 个剩余页面，分为 ${batches.length} 批处理`));

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(chalk.blue(`\n📋 处理第 ${batchIndex + 1}/${batches.length} 批 (${batch.length} 个页面)`));

      await this.validateBatch(batch, batchIndex);

      // 批次间短暂休息
      if (batchIndex < batches.length - 1) {
        console.log(chalk.gray(`   ⏸️  批次间休息 500ms...`));
        await this.sleep(500);
      }
    }
  }

  /**
   * 验证首页
   */
  async validateHomePage() {
    const homeRoute = this.routes.find(route => route.path === '/');

    if (!homeRoute) {
      console.log(chalk.yellow(`⚠️  未找到首页路由 (/)`));
      return { success: true, route: null };
    }

    console.log(chalk.blue(`🏠 优先验证首页: ${homeRoute.path}`));

    const result = await this.validateSinglePageWithConsoleCapture(homeRoute);
    this.validationResults.push(result);

    this.displayPageResult(result, 1, 1);

    return result;
  }

  /**
   * 修复首页问题（如果需要）
   */
  async fixHomePageIfNeeded(homePageResult) {
    if (homePageResult.success || !homePageResult.errors || homePageResult.errors.length === 0) {
      return;
    }

    console.log(chalk.yellow(`🔧 尝试修复首页的 ${homePageResult.errors.length} 个错误...`));

    // 显示首页错误详情
    this.errorAnalyzer.displayErrorSummary(homePageResult.errors, 5);

    const fixResult = await this.attemptPageErrorFix(homePageResult.route, homePageResult.errors);

    if (fixResult.success) {
      console.log(chalk.green(`✅ 首页错误修复成功，修复了 ${fixResult.filesModified} 个文件`));
      homePageResult.fixAttempted = true;
      homePageResult.fixResult = fixResult;

      // 重新验证首页
      if (this.options.revalidateAfterFix) {
        console.log(chalk.gray(`🔄 重新验证首页...`));
        const revalidationResult = await this.validateSinglePageWithConsoleCapture(homePageResult.route);

        if (revalidationResult.success) {
          console.log(chalk.green(`✅ 首页修复后验证成功`));
          // 更新结果
          Object.assign(homePageResult, revalidationResult);
        } else {
          console.log(chalk.yellow(`⚠️  首页修复后仍有问题: ${revalidationResult.errors.length} 个错误`));
        }
      }
    } else {
      console.log(chalk.red(`❌ 首页错误修复失败: ${fixResult.error || '未知原因'}`));
    }
  }

  /**
   * 创建批次
   */
  createBatches(routes, batchSize) {
    const batches = [];
    for (let i = 0; i < routes.length; i += batchSize) {
      batches.push(routes.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 验证一个批次的页面
   */
  async validateBatch(batch, batchIndex) {
    for (let i = 0; i < batch.length; i++) {
      const route = batch[i];
      const globalIndex = this.validationResults.length + 1;
      const totalRoutes = this.routes.length;

      console.log(chalk.gray(`   [${globalIndex}/${totalRoutes}] 验证页面: ${route.path}`));

      const result = await this.validateSinglePageWithConsoleCapture(route);
      this.validationResults.push(result);

      this.displayPageResult(result, globalIndex, totalRoutes);

      // 页面间短暂延迟
      await this.sleep(100);
    }
  }

  /**
   * 显示页面验证结果
   */
  displayPageResult(result, currentIndex, totalRoutes) {
    if (result.success) {
      console.log(chalk.green(`   ✅ 成功`));
      if (this.options.verbose) {
        console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
        if (result.warnings.length > 0) {
          console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
        }
      }
    } else {
      console.log(chalk.red(`   ❌ 失败: ${result.errors.length} 个错误`));

      // 在verbose模式下显示更多信息
      if (this.options.verbose) {
        console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
        console.log(chalk.gray(`      URL: ${result.url}`));
        if (result.warnings.length > 0) {
          console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
        }
        if (result.needsLogin) {
          console.log(chalk.yellow(`      需要登录: ${result.loginAttempted ? '已尝试' : '未尝试'}`));
        }
      }

      // 显示错误摘要（如果之前没有显示过且不是自动修复模式）
      if (!this.options.autoFix || !this.buildFixAgent) {
        this.errorAnalyzer.displayErrorSummary(result.errors, 2);
      }

      if (result.fixAttempted) {
        const fixStatus = result.fixResult?.success ? '成功' : '失败';
        console.log(chalk.yellow(`   🔧 已尝试自动修复: ${fixStatus}`));

        if (this.options.verbose && result.fixResult) {
          if (result.fixResult.success) {
            console.log(chalk.gray(`      修复文件: ${result.fixResult.filesModified || 0} 个`));
          } else {
            console.log(chalk.gray(`      修复失败原因: ${result.fixResult.error || '未知'}`));
          }
        }
      }
    }
  }

  /**
   * 验证单个页面并捕获控制台信息（包括登录时的错误）
   */
  async validateSinglePageWithConsoleCapture(route) {
    const url = this.buildPageUrl(route);
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      loadTime: 0,
      timestamp: new Date().toISOString(),
      needsLogin: false,
      loginAttempted: false,
      loginConsoleErrors: [] // 新增：登录时的控制台错误
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 在页面导航之前立即设置所有监听器
      this.setupPageListenersWithLoginCapture(page, result);

      // 设置页面错误监听
      page.on('pageerror', (error) => {
        const errorMessage = error.message;
        result.errors.push({
          type: 'page-error',
          message: errorMessage,
          timestamp: new Date().toISOString(),
          stack: error.stack
        });

        if (this.options.verbose) {
          console.log(chalk.red(`      🔴 Page Error: ${errorMessage.substring(0, 150)}...`));
        }

        // 检查是否是路由错误
        if (this.isRouteError(errorMessage)) {
          result.loginConsoleErrors.push({
            type: 'page-route-error',
            message: errorMessage,
            timestamp: new Date().toISOString(),
            phase: result.loginAttempted ? 'post-login' : 'pre-login'
          });
        }
      });

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.navigationTimeout);

      // 访问页面并捕获登录阶段的错误
      const navigationResult = await this.navigateWithLoginErrorCapture(page, url, result);

      if (!navigationResult.success) {
        result.errors.push(...navigationResult.errors);
        result.loadTime = Date.now() - startTime;
        await page.close();
        return result;
      }

      // 继续原有的验证逻辑...
      return await this.continuePageValidation(page, result, startTime);

    } catch (error) {
      result.errors.push({
        type: 'validation-error',
        message: `页面验证异常: ${error.message}`,
        stack: error.stack
      });
      result.loadTime = Date.now() - (result.startTime || Date.now());
      return result;
    }
  }

  /**
   * 设置页面监听器（包括登录阶段的控制台捕获）
   */
  setupPageListenersWithLoginCapture(page, result) {
    // 控制台消息监听（包括登录阶段）
    page.on('console', (msg) => {
      const message = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 在详细模式下显示所有console消息
      if (this.options.verbose) {
        const msgType = msg.type();
        const msgText = msg.text();

        if (msgType === 'error') {
          console.log(chalk.red(`      🔴 Console Error: ${msgText.substring(0, 150)}${msgText.length > 150 ? '...' : ''}`));
        } else if (msgType === 'warning') {
          // console.log(chalk.yellow(`      🟡 Console Warning: ${msgText.substring(0, 100)}${msgText.length > 100 ? '...' : ''}`));
        } else if (msgType === 'log' && msgText.includes('error')) {
          console.log(chalk.gray(`      ⚪ Console Log (error-like): ${msgText.substring(0, 100)}${msgText.length > 100 ? '...' : ''}`));
        }
      }

      // 捕获错误级别的控制台消息
      if (msg.type() === 'error') {
        const errorText = msg.text();

        // 检查是否是路由相关错误
        if (this.isRouteError(errorText)) {
          result.loginConsoleErrors.push({
            type: 'route-error',
            message: errorText,
            timestamp: new Date().toISOString(),
            phase: result.loginAttempted ? 'post-login' : 'pre-login'
          });

          if (this.options.verbose) {
            console.log(chalk.red(`      🚨 路由错误检测到: ${errorText.substring(0, 200)}...`));
          }
        }

        // 其他错误也记录
        result.errors.push({
          type: 'console-error',
          message: errorText,
          timestamp: new Date().toISOString(),
          phase: result.loginAttempted ? 'post-login' : 'pre-login'
        });
      }

      // 也检查warning和log消息中是否包含路由错误信息
      if (msg.type() === 'warning' || msg.type() === 'log') {
        const messageText = msg.text();
        if (this.isRouteError(messageText)) {
          result.loginConsoleErrors.push({
            type: `${msg.type()}-route-error`,
            message: messageText,
            timestamp: new Date().toISOString(),
            phase: result.loginAttempted ? 'post-login' : 'pre-login'
          });

          if (this.options.verbose) {
            console.log(chalk.yellow(`      🚨 路由错误(${msg.type()}): ${messageText.substring(0, 200)}...`));
          }
        }
      }
    });

    // 页面错误监听
    page.on('pageerror', (error) => {
      result.errors.push({
        type: 'page-error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        phase: result.loginAttempted ? 'post-login' : 'pre-login'
      });
    });

    // 网络错误监听
    page.on('requestfailed', (request) => {
      result.networkErrors.push({
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error',
        timestamp: new Date().toISOString(),
        phase: result.loginAttempted ? 'post-login' : 'pre-login'
      });
    });
  }

  /**
   * 检查是否是路由相关错误
   */
  isRouteError(errorText) {
    const routeErrorPatterns = [
      /Route paths should start with a "\/"/,
      /should be "\/.*"/,
      /Invalid route/,
      /Router.*error/i,
      /vue-router/i,
      /navigation.*error/i,
      /tokenizePath/i,
      /createRouteRecordMatcher/i,
      /addRoute/i
    ];

    return routeErrorPatterns.some(pattern => pattern.test(errorText));
  }

  /**
   * 导航到页面并捕获登录阶段的错误
   */
  async navigateWithLoginErrorCapture(page, url, result) {
    try {
      // 访问页面
      let response;
      let navigationAttempts = 0;
      const maxNavigationAttempts = 3;

      while (navigationAttempts < maxNavigationAttempts) {
        try {
          response = await page.goto(url, {
            waitUntil: ['domcontentloaded', 'networkidle0'],
            timeout: this.options.navigationTimeout
          });
          break;
        } catch (navError) {
          navigationAttempts++;
          if (navigationAttempts >= maxNavigationAttempts) {
            throw navError;
          }
          console.log(chalk.yellow(`    ⚠️  导航重试 ${navigationAttempts}/${maxNavigationAttempts}: ${navError.message}`));
          await this.sleep(1000);
        }
      }

      // 检查响应状态
      if (response && !response.ok()) {
        result.warnings.push(`HTTP ${response.status()}: ${response.statusText()}`);
      }

      // 等待页面初始化，给足够时间让路由错误显现
      if (this.options.verbose) {
        console.log(chalk.gray(`    ⏳ 等待页面初始化和路由处理...`));
      }
      await this.sleep(3000); // 增加等待时间到3秒

      // 检查是否有早期的路由错误
      if (result.loginConsoleErrors.length > 0) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  检测到 ${result.loginConsoleErrors.length} 个早期路由错误`));
          for (const error of result.loginConsoleErrors) {
            console.log(chalk.red(`      - ${error.message.substring(0, 100)}...`));
          }
        }
      }

      const needsLogin = await this.checkIfNeedsLogin(page);
      result.needsLogin = needsLogin;

      if (needsLogin && !this.options.skipLogin) {
        console.log(chalk.yellow(`    🔐 检测到需要登录，尝试自动登录...`));
        result.loginAttempted = true;

        // 在登录前记录当前的控制台错误数量
        const preLoginErrorCount = result.loginConsoleErrors.length;

        const loginResult = await this.autoLoginManager.attemptLogin(page);

        // 登录后检查是否有新的控制台错误
        const postLoginErrorCount = result.loginConsoleErrors.length;
        if (postLoginErrorCount > preLoginErrorCount) {
          console.log(chalk.yellow(`    ⚠️  登录过程中检测到 ${postLoginErrorCount - preLoginErrorCount} 个控制台错误`));
        }

        if (loginResult.success) {
          console.log(chalk.green(`    ✅ 自动登录成功`));
          this.isLoggedIn = true;

          // 登录成功后等待页面稳定
          await this.sleep(2000);
        } else {
          console.log(chalk.red(`    ❌ 自动登录失败: ${loginResult.error}`));
          result.warnings.push(`自动登录失败: ${loginResult.error}`);
        }
      }

      return { success: true };

    } catch (error) {
      return {
        success: false,
        errors: [{
          type: 'navigation-error',
          message: `页面导航失败: ${error.message}`,
          stack: error.stack,
          timestamp: new Date().toISOString()
        }]
      };
    }
  }

  /**
   * 检查页面是否需要登录
   */
  async checkIfNeedsLogin(page) {
    try {
      const currentUrl = page.url();

      // 检查URL是否包含登录相关路径
      const loginPaths = ['/login', '/signin', '/auth', '/authentication'];
      const isOnLoginPage = loginPaths.some(path => currentUrl.includes(path));

      // 如果在登录页面，说明需要登录
      if (isOnLoginPage) {
        return true;
      }

      // 检查页面是否有登录表单（可能是模态框登录）
      const hasLoginForm = await page.evaluate(() => {
        const loginSelectors = [
          '.login-form',
          '.login-container',
          'form[name="login"]',
          'form[id="login"]',
          'input[name="username"]',
          'input[name="email"]',
          'input[type="email"]',
          'input[placeholder*="用户名"]',
          'input[placeholder*="邮箱"]',
          'input[placeholder*="username"]',
          'input[placeholder*="email"]'
        ];

        return loginSelectors.some(selector => {
          try {
            return document.querySelector(selector) !== null;
          } catch (e) {
            return false;
          }
        });
      });

      // 检查是否有登录相关的文本或按钮
      const hasLoginIndicators = await page.evaluate(() => {
        const loginTexts = ['登录', '登陆', 'Login', 'Sign In', 'Sign in'];
        const bodyText = document.body.textContent || '';

        // 检查是否有登录按钮或链接
        const loginButtons = document.querySelectorAll('button, a, .btn');
        for (const button of loginButtons) {
          const buttonText = button.textContent || button.innerText || '';
          if (loginTexts.some(text => buttonText.includes(text))) {
            return true;
          }
        }

        // 检查页面文本是否包含登录提示
        return loginTexts.some(text => bodyText.includes(text));
      });

      return hasLoginForm || hasLoginIndicators;

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  检查登录状态失败: ${error.message}`));
      }
      return false;
    }
  }

  /**
   * 继续页面验证（原有逻辑的延续）
   */
  async continuePageValidation(page, result, startTime) {
    try {
      // 等待页面渲染和Vue应用初始化
      await this.waitForPageReady(page);

      // 检查页面是否包含 Vue 应用
      const hasVueApp = await page.evaluate(() => {
        return !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
      });

      if (!hasVueApp) {
        result.warnings.push('页面可能未正确加载 Vue 应用');
      }

      // 截图功能
      try {
        const screenshotPath = await this.takeScreenshot(page, result.route);
        result.screenshotPath = screenshotPath;
        if (this.options.verbose) {
          console.log(chalk.gray(`    📸 页面截图已保存: ${screenshotPath}`));
        }
      } catch (screenshotError) {
        result.warnings.push(`截图失败: ${screenshotError.message}`);
      }

      // 继续原有的错误检测逻辑...
      result.loadTime = Date.now() - startTime;

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      } else if (this.options.autoFix && this.buildFixAgent) {
        // 尝试自动修复
        const fixResult = await this.attemptPageErrorFix(result.route, result.errors);
        if (fixResult.success) {
          result.fixAttempted = true;
          result.fixResult = fixResult;
        }
      }

      await page.close();
      return result;

    } catch (error) {
      result.errors.push({
        type: 'validation-continuation-error',
        message: `页面验证继续过程出错: ${error.message}`,
        stack: error.stack
      });
      result.loadTime = Date.now() - startTime;
      await page.close();
      return result;
    }
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    if (this.options.baseUrl) {
      console.log(chalk.gray(`   使用外部服务器: ${this.baseUrl}`));
      return;
    }

    console.log(chalk.gray(`   启动开发服务器...`));

    return new Promise((resolve, reject) => {
      // 解析开发命令
      const [command, ...args] = this.options.devCommand.split(' ');

      this.devServer = spawn(command, args, {
        cwd: this.projectPath,
        stdio: 'pipe', // 总是使用 pipe 以便监听输出
        env: {
          ...process.env,
          PORT: this.options.port.toString()
        }
      });

      let serverReady = false;
      let output = '';
      let detectedPort = null;

      // 监听输出判断服务器是否启动
      if (this.devServer.stdout) {
        this.devServer.stdout.on('data', (data) => {
          const text = data.toString();
          output += text;

          if (this.options.verbose) {
            console.log(text);
          }

          // 添加更多调试信息
          // if (this.options.verbose && !serverReady) {
          //   console.log(chalk.cyan(`   🔍 检查文本: "${text.trim()}"`));
          //   console.log(chalk.cyan(`   🔍 包含 'Local:': ${text.includes('Local:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'localhost:': ${text.includes('localhost:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'App running at': ${text.includes('App running at')}`));
          // }

          // 检查服务器启动标志并提取端口
          if (!serverReady && (
            text.includes('Local:') ||
            text.includes('localhost:') ||
            text.includes('App running at') ||
            text.includes('Network:')
          )) {
            if (this.options.verbose) {
              console.log(chalk.blue(`   🔍 检测到服务器启动信息: ${text.trim()}`));
            }

            // 尝试提取端口号
            const portMatch = text.match(/localhost:(\d+)/);
            if (portMatch) {
              detectedPort = parseInt(portMatch[1]);
              if (this.options.verbose) {
                console.log(chalk.gray(`   检测到服务器端口: ${detectedPort}`));
              }

              // 验证服务器是否真的可以访问
              this.verifyServerWithRetry(detectedPort, resolve, reject);
            }
          }
        });
      }

      if (this.devServer.stderr) {
        this.devServer.stderr.on('data', (data) => {
          const text = data.toString();
          if (this.options.verbose) {
            console.error(chalk.red(text));
          }
        });
      }

      this.devServer.on('error', (error) => {
        reject(new Error(`启动开发服务器失败: ${error.message}`));
      });

      this.devServer.on('exit', (code) => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`开发服务器异常退出，代码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error(`开发服务器启动超时 (${this.options.waitForServer}ms)`));
        }
      }, this.options.waitForServer);
    });
  }

  /**
   * 带重试的服务器验证
   */
  async verifyServerWithRetry(port, resolve, reject, attempt = 1, maxAttempts = 5) {
    const delay = attempt * 2000; // 递增延迟：2s, 4s, 6s, 8s, 10s

    setTimeout(async () => {
      try {
        const isReady = await this.verifyServerReady(port);

        if (isReady) {
          this.options.port = port; // 更新实际端口
          this.baseUrl = `http://localhost:${port}`;
          if (this.options.verbose) {
            console.log(chalk.green(`   ✅ 服务器验证成功 (端口: ${port}, 尝试: ${attempt}/${maxAttempts})`));
          }
          resolve();
        } else if (attempt < maxAttempts) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`   ⚠️  端口 ${port} 验证失败，${delay/1000}秒后重试 (${attempt}/${maxAttempts})...`));
          }
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        } else {
          if (this.options.verbose) {
            console.log(chalk.red(`   ❌ 服务器验证失败，已达到最大重试次数 (${maxAttempts})`));
          }
          // 最后一次尝试失败，但不要 reject，让超时处理
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`   ⚠️  服务器验证出错 (尝试 ${attempt}/${maxAttempts}): ${error.message}`));
        }
        if (attempt < maxAttempts) {
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        }
      }
    }, delay);
  }

  /**
   * 验证服务器是否真的可以访问
   */
  async verifyServerReady(port) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://localhost:${port}`, {
        timeout: 5000,
        validateStatus: () => true // 接受任何状态码
      });

      return response.status < 500; // 只要不是服务器错误就认为可用
    } catch (error) {
      return false;
    }
  }

  /**
   * 确保浏览器可用
   */
  async ensureBrowser() {
    try {
      const selectedBrowser = await this.browserDetector.ensureBrowser();

      // 如果选择的是系统浏览器，设置可执行文件路径
      if (selectedBrowser.type !== 'puppeteer') {
        this.options.executablePath = selectedBrowser.executablePath;
      }

      // 设置浏览器类型以便后续创建正确的适配器
      this.selectedBrowserType = selectedBrowser.type;

      return selectedBrowser;
    } catch (error) {
      throw new Error(`浏览器检测失败: ${error.message}`);
    }
  }

  /**
   * 创建浏览器自动化实例
   */
  async createBrowserAutomation() {
    try {
      // 如果已经有选择的浏览器类型，使用它
      if (this.selectedBrowserType) {
        return browserFactory.createBrowserAutomation(this.selectedBrowserType);
      }

      // 否则自动选择最佳的浏览器
      const preferredTypes = ['puppeteer', 'playwright-chromium', 'playwright-firefox', 'playwright-webkit'];
      return await browserFactory.createConfiguredBrowserAutomation({
        preferredTypes,
        autoSelect: true
      });
    } catch (error) {
      // 如果自动选择失败，回退到默认的 Puppeteer
      console.log(chalk.yellow(`⚠️  自动选择浏览器失败，使用默认 Puppeteer: ${error.message}`));
      return browserFactory.createBrowserAutomation('puppeteer');
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.gray(`   启动浏览器...`));

    // 确保浏览器可用
    await this.ensureBrowser();

    const launchOptions = {
      headless: this.options.headless === 'new' ? 'new' : this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    };

    // 如果检测到系统浏览器，使用它
    if (this.options.executablePath) {
      launchOptions.executablePath = this.options.executablePath;
    }

    try {
      // 使用抽象层启动浏览器
      this.browserAutomation = await this.createBrowserAutomation();
      this.browser = await this.browserAutomation.launch(launchOptions);
      console.log(chalk.green('✅ 浏览器启动成功'));
    } catch (error) {
      // 如果启动失败，尝试使用新的 headless 模式
      if (!launchOptions.headless || launchOptions.headless === true) {
        console.log(chalk.yellow('⚠️  尝试使用新的 headless 模式...'));
        launchOptions.headless = 'new';
        this.browser = await this.browserAutomation.launch(launchOptions);
        console.log(chalk.green('✅ 浏览器启动成功 (新 headless 模式)'));
      } else {
        throw error;
      }
    }
  }

  /**
   * 构建页面URL
   */
  buildPageUrl(route) {
    if (this.options.routerMode === 'hash') {
      // Hash模式: http://localhost:9527/#/example/list
      return `${this.baseUrl}/#${route.path}`;
    } else {
      // History模式: http://localhost:9527/example/list
      return `${this.baseUrl}${route.path}`;
    }
  }

  /**
   * 验证单个页面
   */
  async validateSinglePage(route) {
    const url = this.buildPageUrl(route);
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      loadTime: 0,
      timestamp: new Date().toISOString(),
      needsLogin: false,
      loginAttempted: false
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 设置页面事件监听
      this.setupPageListeners(page, result);

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.navigationTimeout);

      // 访问页面 - 使用更宽松的等待条件和重试机制
      let response;
      let navigationAttempts = 0;
      const maxNavigationAttempts = 3;

      while (navigationAttempts < maxNavigationAttempts) {
        try {
          if (this.options.verbose && navigationAttempts > 0) {
            console.log(chalk.gray(`    🔄 重试页面导航 (第 ${navigationAttempts + 1} 次尝试)...`));
          }

          response = await page.goto(url, {
            waitUntil: 'domcontentloaded', // 使用较快的等待条件
            timeout: this.options.navigationTimeout
          });
          break; // 成功则跳出循环
        } catch (navigationError) {
          navigationAttempts++;
          if (navigationAttempts >= maxNavigationAttempts) {
            throw navigationError; // 最后一次尝试失败则抛出错误
          }

          if (this.options.verbose) {
            console.log(chalk.yellow(`    ⚠️  页面导航失败: ${navigationError.message}，将重试...`));
          }

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      result.loadTime = Date.now() - startTime;

      // 检查响应状态
      /// todo skip 304 in future
      // if (!response.ok()) {
      //   result.errors.push(`HTTP ${response.status()}: ${response.statusText()}`);
      // }

      // 检查是否被重定向到登录页面
      const finalUrl = page.url();
      if (finalUrl.includes('/login') && !url.includes('/login')) {
        result.needsLogin = true;

        if (this.options.verbose) {
          console.log(chalk.yellow(`    🔐 页面被重定向到登录页面: ${finalUrl}`));
        }

        // 如果还没有登录或登录失效，尝试登录
        if (!this.isLoggedIn && !this.options.skipLogin) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`    🔑 尝试登录后重新访问页面...`));
          }

          result.loginAttempted = true;
          const loginSuccess = await this.autoLoginManager.attemptLogin(page);

          if (loginSuccess) {
            this.isLoggedIn = true;

            if (this.options.verbose) {
              console.log(chalk.green(`    🔄 登录成功，重新访问目标页面...`));
            }

            // 登录成功后重新访问目标页面
            await page.goto(url, {
              waitUntil: 'domcontentloaded',
              timeout: this.options.pageTimeout
            });

            const retryUrl = page.url();
            if (!retryUrl.includes('/login')) {
              if (this.options.verbose) {
                console.log(chalk.green(`    ✅ 登录后成功访问页面: ${retryUrl}`));
              }
            } else {
              result.errors.push('登录后仍被重定向到登录页面，可能是权限不足');
              this.isLoggedIn = false; // 重置登录状态
            }
          } else {
            result.errors.push('页面需要登录权限，自动登录失败');
          }
        } else if (this.isLoggedIn) {
          // 已经登录但仍被重定向，可能是登录失效或权限不足
          if (this.options.verbose) {
            console.log(chalk.yellow(`    ⚠️  已登录但被重定向，可能登录失效，重新尝试登录...`));
          }

          // 重置登录状态并重新尝试
          this.isLoggedIn = false;
          result.loginAttempted = true;
          const loginSuccess = await this.autoLoginManager.attemptLogin(page);

          if (loginSuccess) {
            this.isLoggedIn = true;
            // 重新访问目标页面
            await page.goto(url, {
              waitUntil: 'domcontentloaded',
              timeout: this.options.pageTimeout
            });

            const retryUrl = page.url();
            if (retryUrl.includes('/login')) {
              result.errors.push('页面需要特定权限，当前用户权限不足');
              this.isLoggedIn = false;
            }
          } else {
            result.errors.push('登录失效，重新登录失败');
          }
        } else {
          result.errors.push('页面需要登录权限，跳过了自动登录');
        }
      }

      // 等待页面渲染和Vue应用初始化 - 使用智能等待策略
      await this.waitForPageReady(page);

      // 检查页面是否包含 Vue 应用
      const hasVueApp = await page.evaluate(() => {
        return !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
      });

      if (!hasVueApp) {
        result.warnings.push('页面可能未正确加载 Vue 应用');
      }

      // 截图功能 - 为每个页面截图
      try {
        const screenshotPath = await this.takeScreenshot(page, route);
        result.screenshotPath = screenshotPath;
        if (this.options.verbose) {
          console.log(chalk.gray(`    📸 页面截图已保存: ${screenshotPath}`));
        }
      } catch (screenshotError) {
        result.warnings.push(`截图失败: ${screenshotError.message}`);
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  截图失败: ${screenshotError.message}`));
        }
      }

      // 主动检测错误弹框和错误信息
      const errorDetection = await page.evaluate(() => {
        const errors = [];

        // 检查是否有包含"Uncaught runtime errors"的元素
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
          const text = element.textContent || element.innerText || '';

          // 检查错误弹框
          if (text.includes('Uncaught runtime errors') ||
              text.includes('Cannot read properties of null') ||
              text.includes('TypeError:') ||
              text.includes('ReferenceError:') ||
              text.includes('Error:')) {
            errors.push({
              type: 'error-dialog',
              message: text.substring(0, 500),
              tagName: element.tagName,
              className: element.className,
              id: element.id
            });
          }
        }

        // 检查页面标题是否包含错误
        if (document.title.includes('Error') || document.title.includes('错误')) {
          errors.push({
            type: 'page-title-error',
            message: `页面标题包含错误: ${document.title}`
          });
        }

        // 检查控制台是否有错误（通过检查是否有错误样式的元素）
        const errorStyleElements = document.querySelectorAll([
          '.error',
          '.Error',
          '[class*="error"]',
          '[class*="Error"]',
          '.runtime-error',
          '.js-error'
        ].join(','));

        for (const element of errorStyleElements) {
          const text = element.textContent || element.innerText || '';
          if (text.length > 10) {
            errors.push({
              type: 'error-styled-element',
              message: text.substring(0, 300),
              tagName: element.tagName,
              className: element.className
            });
          }
        }

        return errors;
      });

      // 将检测到的错误添加到结果中
      for (const error of errorDetection) {
        result.errors.push({
          type: 'dom-error-detection',
          message: error.message,
          details: error
        });
      }

      // 多次检查错误，因为有些错误可能延迟出现
      let errorData = { pageErrors: [], vueErrors: [], consoleErrors: [] };

      // 第一次检查
      const firstCheck = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 合并第一次检查的结果（安全检查）
      if (firstCheck && firstCheck.pageErrors) {
        errorData.pageErrors.push(...firstCheck.pageErrors);
      }
      if (firstCheck && firstCheck.vueErrors) {
        errorData.vueErrors.push(...firstCheck.vueErrors);
      }
      if (firstCheck && firstCheck.consoleErrors) {
        errorData.consoleErrors.push(...firstCheck.consoleErrors);
      }

      // 等待异步错误出现 - 进一步减少等待时间
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 第二次检查
      const secondCheck = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 合并新的错误（避免重复）
      const existingErrorMessages = new Set(errorData.pageErrors.map(e => e.message));
      const existingVueMessages = new Set(errorData.vueErrors.map(e => e.message));
      const existingConsoleMessages = new Set(errorData.consoleErrors.map(e => e.message));

      // 安全检查第二次检查的结果
      if (secondCheck && secondCheck.pageErrors) {
        for (const error of secondCheck.pageErrors) {
          if (!existingErrorMessages.has(error.message)) {
            errorData.pageErrors.push(error);
          }
        }
      }

      if (secondCheck && secondCheck.vueErrors) {
        for (const error of secondCheck.vueErrors) {
          if (!existingVueMessages.has(error.message)) {
            errorData.vueErrors.push(error);
          }
        }
      }

      if (secondCheck && secondCheck.consoleErrors) {
        for (const error of secondCheck.consoleErrors) {
          if (!existingConsoleMessages.has(error.message)) {
            errorData.consoleErrors.push(error);
          }
        }
      }

      // 处理页面错误
      if (errorData.pageErrors.length > 0) {
        for (const error of errorData.pageErrors) {
          const errorMessage = error.message || 'Unknown error';
          // 只添加有效的代码错误，并避免重复
          if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
            result.errors.push({
              type: error.type || 'javascript-error',
              message: errorMessage,
              details: error
            });
          }
        }
      }

      // 处理 Vue 错误
      if (errorData.vueErrors.length > 0) {
        for (const error of errorData.vueErrors) {
          const errorMessage = error.message || 'Unknown Vue error';
          if (error.type && error.type.includes('warning')) {
            result.warnings.push(`Vue Warning: ${errorMessage}`);
          } else {
            // 只添加有效的代码错误，并避免重复
            if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
              result.errors.push({
                type: 'vue-error',
                message: errorMessage,
                details: error
              });
            }
          }
        }
      }

      // 处理控制台错误
      if (errorData.consoleErrors.length > 0) {
        for (const error of errorData.consoleErrors) {
          const errorMessage = error.message || 'Unknown console error';
          if (error.level === 'error') {
            // 只添加有效的代码错误，并避免重复
            if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
              result.errors.push({
                type: 'console-error',
                message: errorMessage,
                details: error
              });
            }
          } else if (error.level === 'warn') {
            result.warnings.push(`Console Warning: ${errorMessage}`);
          }
        }
      }

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      } else if (this.options.autoFix && this.buildFixAgent) {
        // 如果启用了自动修复且有错误，尝试使用 BuildFixAgent 修复
        console.log(chalk.yellow(`  🔧 检测到 ${result.errors.length} 个错误，尝试自动修复...`));

        // 显示前几个错误信息用于调试（不管是否为详细模式）
        this.errorAnalyzer.displayErrorSummary(result.errors, 5);

        const fixResult = await this.attemptPageErrorFix(route, result.errors);

        if (fixResult.success) {
          console.log(chalk.green(`  ✅ 页面错误修复成功，修复了 ${fixResult.filesModified} 个文件`));
          result.fixAttempted = true;
          result.fixResult = fixResult;

          // 可选：重新验证页面以确认修复效果
          if (this.options.revalidateAfterFix) {
            console.log(chalk.gray(`  🔄 重新验证页面...`));

            try {
              // 等待一段时间让修复生效
              await new Promise(resolve => setTimeout(resolve, 2000));

              // 重新加载页面
              await page.reload({
                waitUntil: 'domcontentloaded',
                timeout: this.options.navigationTimeout
              });

              // 等待页面准备就绪
              await this.waitForPageReady(page);

              // 重新检查错误
              const revalidationErrors = await this.checkPageErrors(page);
              if (revalidationErrors.length < result.errors.length) {
                console.log(chalk.green(`  ✅ 修复后错误减少: ${result.errors.length} → ${revalidationErrors.length}`));
                result.errorsAfterFix = revalidationErrors;
                if (revalidationErrors.length === 0) {
                  result.success = true;
                }
              } else {
                console.log(chalk.yellow(`  ⚠️  修复后错误数量未减少: ${result.errors.length} → ${revalidationErrors.length}`));
                // 显示修复后剩余的错误
                if (revalidationErrors.length > 0) {
                  console.log(chalk.gray(`    📋 剩余错误:`));
                  this.errorAnalyzer.displayErrorSummary(revalidationErrors, 3);
                }
              }
            } catch (revalidationError) {
              console.log(chalk.yellow(`  ⚠️  重新验证失败: ${revalidationError.message}`));
            }
          }
        } else {
          // 根据失败原因显示不同的消息
          if (fixResult.filteredCount > 0) {
            console.log(chalk.yellow(`  ⚠️  ${fixResult.filteredCount} 个错误无法修复（非代码错误），${fixResult.error}`));
          } else {
            console.log(chalk.yellow(`  ⚠️  页面错误修复失败: ${fixResult.error}`));
          }
          result.fixAttempted = true;
          result.fixResult = fixResult;

          // 即使修复失败，也显示详细的错误信息
          if (this.options.verbose) {
            console.log(chalk.gray(`    🔍 修复失败详情:`));
            if (fixResult.analysisResult) {
              console.log(chalk.gray(`      - 分析结果: ${JSON.stringify(fixResult.analysisResult, null, 2).substring(0, 200)}...`));
            }
            if (fixResult.fixResult) {
              console.log(chalk.gray(`      - 修复结果: ${JSON.stringify(fixResult.fixResult, null, 2).substring(0, 200)}...`));
            }
          }
        }
      } else {
        // 如果没有启用自动修复，也显示错误摘要
        this.errorAnalyzer.displayErrorSummary(result.errors, 5);

        if (this.options.verbose) {
          console.log(chalk.gray(`    💡 提示: 使用 --auto-fix 选项可以尝试自动修复这些错误`));
        }
      }

      await page.close();

    } catch (error) {
      result.errors.push(`页面访问失败: ${error.message}`);

      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${route.path}: ${error.message}`));
      }
    }

    return result;
  }

  /**
   * 智能等待页面准备就绪
   */
  async waitForPageReady(page) {
    const maxWaitTime = 5000; // 最大等待5秒
    const checkInterval = 200; // 每200ms检查一次
    let waitTime = 0;

    while (waitTime < maxWaitTime) {
      try {
        // 检查页面是否已经准备就绪
        const isReady = await page.evaluate(() => {
          // 检查DOM是否加载完成
          if (document.readyState !== 'complete') {
            return false;
          }

          // 检查是否有Vue应用
          const hasVue = !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));

          // 检查是否有明显的加载指示器
          const loadingElements = document.querySelectorAll([
            '.loading',
            '.spinner',
            '.loader',
            '[class*="loading"]',
            '[class*="spinner"]'
          ].join(','));

          const hasVisibleLoading = Array.from(loadingElements).some(el => {
            const style = window.getComputedStyle(el);
            return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
          });

          // 如果有Vue应用且没有可见的加载指示器，认为页面准备就绪
          return hasVue && !hasVisibleLoading;
        });

        if (isReady) {
          if (this.options.verbose) {
            console.log(chalk.gray(`    ✅ 页面准备就绪 (等待时间: ${waitTime}ms)`));
          }
          return;
        }

        // 等待一段时间后再次检查
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;

      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`    ⚠️  页面准备检查出错: ${error.message}`));
        }
        break;
      }
    }

    // 如果超时，使用固定等待时间
    if (waitTime >= maxWaitTime) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`    ⚠️  页面准备检查超时，使用固定等待时间`));
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  /**
   * 为页面截图
   */
  async takeScreenshot(page, route) {
    // 确保截图目录存在
    const screenshotDir = path.join(this.projectPath, 'validation-reports', 'screenshots');
    await fs.ensureDir(screenshotDir);

    // 生成截图文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const routeName = route.path.replace(/[\/\?#]/g, '_').replace(/^_/, '') || 'root';
    const filename = `${routeName}_${timestamp}.png`;
    const screenshotPath = path.join(screenshotDir, filename);

    // 设置视口大小
    await page.setViewport({ width: 1280, height: 800 });

    // 截图
    await page.screenshot({
      path: screenshotPath,
      fullPage: true,
      type: 'png'
    });

    return screenshotPath;
  }

  /**
   * 设置页面事件监听器
   */
  setupPageListeners(page, result) {
    // 监听控制台消息
    page.on('console', async (msg) => {
      let messageText = msg.text();

      // 如果是 JSHandle 类型的错误，尝试获取更详细的信息
      if (messageText.includes('JSHandle@') && msg.args().length > 0) {
        try {
          const args = msg.args();
          const detailedMessages = [];
          let fullStackTrace = null;

          for (const arg of args) {
            try {
              // 首先尝试获取完整的错误对象信息，包括堆栈跟踪
              const errorInfo = await arg.evaluate(obj => {
                if (obj instanceof Error) {
                  return {
                    message: obj.message,
                    stack: obj.stack,
                    name: obj.name,
                    fileName: obj.fileName,
                    lineNumber: obj.lineNumber,
                    columnNumber: obj.columnNumber
                  };
                } else if (obj && typeof obj === 'object' && obj.stack) {
                  // 处理类似错误对象的结构
                  return {
                    message: obj.message || obj.toString(),
                    stack: obj.stack,
                    name: obj.name || 'Error'
                  };
                } else if (obj && typeof obj === 'object') {
                  return {
                    message: obj.message || JSON.stringify(obj),
                    toString: obj.toString()
                  };
                } else {
                  return {
                    message: String(obj)
                  };
                }
              });

              if (errorInfo.stack) {
                fullStackTrace = errorInfo.stack;
                // 从完整堆栈中提取第一行作为主要错误信息
                const firstLine = errorInfo.stack.split('\n')[0];
                detailedMessages.push(firstLine);
              } else if (errorInfo.message) {
                detailedMessages.push(errorInfo.message);
              }

            } catch (evaluateError) {
              // 如果evaluate失败，回退到原有逻辑
              try {
                const jsonValue = await arg.jsonValue();
                if (jsonValue && typeof jsonValue === 'object') {
                  if (jsonValue.message) {
                    detailedMessages.push(jsonValue.message);
                  } else if (jsonValue.stack) {
                    fullStackTrace = jsonValue.stack;
                    detailedMessages.push(jsonValue.stack.split('\n')[0]);
                  } else {
                    detailedMessages.push(JSON.stringify(jsonValue));
                  }
                } else {
                  detailedMessages.push(String(jsonValue));
                }
              } catch (e) {
                // 如果无法获取 JSON 值，尝试获取字符串表示
                try {
                  const stringValue = await arg.evaluate(obj => {
                    // 尝试获取堆栈信息
                    if (obj && obj.stack) {
                      return obj.stack;
                    }
                    return obj.toString();
                  });

                  // 检查字符串是否包含堆栈信息
                  if (stringValue.includes('\n    at ')) {
                    fullStackTrace = stringValue;
                    detailedMessages.push(stringValue.split('\n')[0]);
                  } else {
                    detailedMessages.push(stringValue);
                  }
                } catch (e2) {
                  detailedMessages.push(messageText);
                }
              }
            }
          }

          if (detailedMessages.length > 0 && detailedMessages[0] !== messageText) {
            messageText = detailedMessages.join(' | ');
          }

          // 如果获取到完整堆栈，将其添加到消息中
          if (fullStackTrace && !messageText.includes(fullStackTrace)) {
            messageText += '\n' + fullStackTrace;
          }

        } catch (e) {
          // 如果获取详细信息失败，保持原始消息
        }
      }

      const message = {
        type: msg.type(),
        text: messageText,
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 只处理 Error 级别的错误，过滤掉 Warning 和无意义的错误
      if (msg.type() === 'error') {
        // 过滤掉无意义的错误信息
        if (this.errorAnalyzer.isValidCodeError(messageText)) {
          result.errors.push(`Console Error: ${messageText}`);
        }
      } else if (msg.type() === 'warning') {
        // 只收集警告，不当作错误处理
        result.warnings.push(`Console Warning: ${messageText}`);
      }
    });

    // 监听页面错误
    page.on('pageerror', (error) => {
      let errorMessage = error.message;

      // 尝试获取完整的错误信息，包括堆栈跟踪
      if (error.stack) {
        errorMessage = error.stack;
      } else if (error.toString && error.toString() !== error.message) {
        errorMessage = error.toString();
      }

      // 过滤掉重复的页面错误
      if (this.errorAnalyzer.isValidCodeError(errorMessage) && !this.errorAnalyzer.isDuplicateError(result.errors, errorMessage)) {
        result.errors.push(`Page Error: ${errorMessage}`);
      }
    });

    // 监听请求失败
    page.on('requestfailed', (request) => {
      const networkError = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error'
      };

      result.networkErrors.push(networkError);

      // 只有关键资源失败才算错误
      if (request.resourceType() === 'document' || request.resourceType() === 'script') {
        result.errors.push(`Network Error: ${request.url()} - ${networkError.failure}`);
      }
    });

    // 注入增强的错误收集脚本
    page.evaluateOnNewDocument(() => {
      window.__pageErrors = [];
      window.__vueErrors = [];
      window.__consoleErrors = [];

      // 捕获全局错误
      window.addEventListener('error', (event) => {
        const errorInfo = {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          timestamp: new Date().toISOString(),
          type: 'javascript-error'
        };
        window.__pageErrors.push(errorInfo);
        console.error('Captured global error:', errorInfo);
      });

      // 捕获 Promise 错误
      window.addEventListener('unhandledrejection', (event) => {
        const errorInfo = {
          message: `Unhandled Promise Rejection: ${event.reason}`,
          reason: event.reason,
          stack: event.reason?.stack,
          timestamp: new Date().toISOString(),
          type: 'promise-rejection'
        };
        window.__pageErrors.push(errorInfo);
        console.error('Captured promise rejection:', errorInfo);
      });

      // 拦截 console.error 和 console.warn
      const originalError = console.error;
      const originalWarn = console.warn;

      console.error = function(...args) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        window.__consoleErrors.push({
          level: 'error',
          message: message,
          timestamp: new Date().toISOString(),
          args: args
        });
        originalError.apply(console, args);
      };

      console.warn = function(...args) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        window.__consoleErrors.push({
          level: 'warn',
          message: message,
          timestamp: new Date().toISOString(),
          args: args
        });
        originalWarn.apply(console, args);
      };

      // Vue 错误处理器设置（延迟执行以确保 Vue 已加载）
      function setupVueErrorHandlers() {
        if (typeof window !== 'undefined') {
          // Vue 3 应用错误处理 - 更全面的方法
          if (window.Vue && window.Vue.createApp) {
            const originalCreateApp = window.Vue.createApp;
            window.Vue.createApp = function(...args) {
              const app = originalCreateApp.apply(this, args);

              app.config.errorHandler = (err, instance, info) => {
                const errorInfo = {
                  message: err.message || err.toString(),
                  stack: err.stack,
                  info: info,
                  componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                  timestamp: new Date().toISOString(),
                  type: 'vue3-error'
                };
                window.__vueErrors.push(errorInfo);
                console.error('Vue 3 Error captured:', errorInfo);
              };

              app.config.warnHandler = (msg, instance, trace) => {
                const warnInfo = {
                  message: msg,
                  trace: trace,
                  componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                  timestamp: new Date().toISOString(),
                  type: 'vue3-warning'
                };
                window.__vueErrors.push(warnInfo);
                console.warn('Vue 3 Warning captured:', warnInfo);
              };

              return app;
            };
          }

          // 尝试直接在已存在的Vue应用上设置错误处理器
          if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__ && window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps) {
            window.__VUE_DEVTOOLS_GLOBAL_HOOK__.apps.forEach(app => {
              if (app.config) {
                app.config.errorHandler = (err, instance, info) => {
                  const errorInfo = {
                    message: err.message || err.toString(),
                    stack: err.stack,
                    info: info,
                    componentName: instance?.$options?.name || instance?.__name || 'Unknown',
                    timestamp: new Date().toISOString(),
                    type: 'vue3-existing-app-error'
                  };
                  window.__vueErrors.push(errorInfo);
                  console.error('Vue 3 Existing App Error captured:', errorInfo);
                };
              }
            });
          }

          // Vue 2 全局错误处理
          if (window.Vue && window.Vue.config) {
            window.Vue.config.errorHandler = function(err, vm, info) {
              window.__vueErrors.push({
                message: err.message || err.toString(),
                stack: err.stack,
                info: info,
                componentName: vm?.$options?.name || 'Unknown',
                timestamp: new Date().toISOString(),
                type: 'vue2-error'
              });
            };

            window.Vue.config.warnHandler = function(msg, vm, trace) {
              window.__vueErrors.push({
                message: msg,
                trace: trace,
                componentName: vm?.$options?.name || 'Unknown',
                timestamp: new Date().toISOString(),
                type: 'vue2-warning'
              });
            };
          }
        }
      }

      // 立即尝试设置
      setupVueErrorHandlers();

      // 延迟设置以确保 Vue 已加载
      setTimeout(setupVueErrorHandlers, 100);
      setTimeout(setupVueErrorHandlers, 500);
      setTimeout(setupVueErrorHandlers, 1000);
      setTimeout(setupVueErrorHandlers, 2000);

      // 主动检测页面错误的函数
      function detectPageErrors() {
        // 检查是否有错误弹框或错误信息
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '[id*="error"]',
          '[id*="Error"]',
          '.error-message',
          '.error-dialog',
          '.error-modal',
          '.runtime-error',
          '[data-testid*="error"]'
        ].join(','));

        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('错误') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError') ||
            text.includes('ReferenceError') ||
            text.includes('Uncaught')
          )) {
            window.__pageErrors.push({
              message: `DOM Error Element: ${text.substring(0, 200)}`,
              element: element.tagName + (element.className ? '.' + element.className : ''),
              timestamp: new Date().toISOString(),
              type: 'dom-error-element'
            });
          }
        }

        // 检查页面标题是否包含错误信息
        if (document.title && (
          document.title.includes('Error') ||
          document.title.includes('错误') ||
          document.title.includes('404') ||
          document.title.includes('500')
        )) {
          window.__pageErrors.push({
            message: `Page Title Error: ${document.title}`,
            timestamp: new Date().toISOString(),
            type: 'page-title-error'
          });
        }
      }

      // 定期检测页面错误
      setTimeout(detectPageErrors, 1000);
      setTimeout(detectPageErrors, 3000);
      setTimeout(detectPageErrors, 5000);
      setTimeout(detectPageErrors, 8000);

      // 监听DOM变化，检测新出现的错误元素
      const observer = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            for (const node of mutation.addedNodes) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const text = node.textContent || node.innerText || '';
                if (text.includes('Uncaught runtime errors') ||
                    text.includes('Cannot read properties of null') ||
                    text.includes('TypeError') ||
                    text.includes('Error')) {
                  window.__pageErrors.push({
                    message: `DOM Mutation Error: ${text.substring(0, 300)}`,
                    timestamp: new Date().toISOString(),
                    type: 'dom-mutation-error'
                  });
                }
              }
            }
          }
        }
      });

      // 确保document.body存在后再观察
      if (document.body) {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      } else {
        // 如果body还没有加载，等待DOM加载完成
        document.addEventListener('DOMContentLoaded', () => {
          if (document.body) {
            observer.observe(document.body, {
              childList: true,
              subtree: true
            });
          }
        });
      }
    });
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    const totalPages = this.validationResults.length;
    const successfulPages = this.validationResults.filter(r => r.success).length;
    const failedPages = this.validationResults.filter(r => !r.success);

    // 收集所有错误进行 FAQ 分析
    const allErrors = [];
    const routeErrors = [];

    for (const result of this.validationResults) {
      if (result.errors && result.errors.length > 0) {
        allErrors.push(...result.errors);
      }

      // 收集路由错误
      if (result.loginConsoleErrors && result.loginConsoleErrors.length > 0) {
        routeErrors.push(...result.loginConsoleErrors);
        // 将路由错误也加入到总错误列表中
        allErrors.push(...result.loginConsoleErrors);
      }
    }

    // 进行 FAQ 分析
    const faqAnalysis = this.faqHelper.analyzeErrors(allErrors);

    const report = {
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0,
        routeErrors: routeErrors.length // 添加路由错误统计
      },
      results: this.validationResults,
      failedPages: failedPages,
      routeErrors: routeErrors, // 添加路由错误详情
      faqAnalysis: faqAnalysis,
      timestamp: new Date().toISOString()
    };

    return report;
  }

  /**
   * 打印验证摘要
   */
  printSummary() {
    const report = this.generateReport();

    console.log(chalk.blue('\n📊 验证结果摘要:'));
    console.log(chalk.gray(`   总页面数: ${report.summary.total}`));
    console.log(chalk.green(`   成功: ${report.summary.successful}`));
    console.log(chalk.red(`   失败: ${report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${report.summary.successRate}%`));

    // 显示路由错误统计
    if (report.summary.routeErrors > 0) {
      console.log(chalk.yellow(`   路由错误: ${report.summary.routeErrors} 个`));
    }

    if (report.failedPages.length > 0) {
      console.log(chalk.red('\n❌ 失败的页面:'));
      for (const failed of report.failedPages) {
        console.log(chalk.red(`   ${failed.route.path}: ${failed.errors.length} 个错误`));

        // 显示错误的详细信息（改进版）
        if (failed.errors.length > 0) {
          const formattedErrors = this.errorAnalyzer.formatErrorsForDisplay(failed.errors);
          formattedErrors.forEach((errorInfo, index) => {
            console.log(chalk.red(`     ${index + 1}. ${errorInfo.type}: ${errorInfo.summary}`));
            if (errorInfo.details && this.options.verbose) {
              console.log(chalk.gray(`        详情: ${errorInfo.details}`));
            }
          });

          if (failed.errors.length > formattedErrors.length) {
            console.log(chalk.gray(`     ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误`));
          }
        }
      }

      // 显示 FAQ 分析结果
      if (report.faqAnalysis && report.faqAnalysis.suggestions.length > 0) {
        console.log(chalk.blue('\n💡 常见问题解决建议:'));
        console.log(chalk.gray(report.faqAnalysis.summary));

        // 显示前3个最相关的建议
        const topSuggestions = report.faqAnalysis.suggestions.slice(0, 3);
        for (const suggestion of topSuggestions) {
          const count = report.faqAnalysis.errorCounts.get(suggestion.solution) || 0;
          console.log(chalk.yellow(`   • ${suggestion.solution} (${count} 个相关错误)`));

          if (suggestion.summary && suggestion.summary.cause) {
            console.log(chalk.gray(`     原因: ${suggestion.summary.cause}`));
          }
        }

        console.log(chalk.gray('\n   📖 详细解决方案请参考: docs/runtime-faq-summary.md'));
      }
    }

    // 显示路由错误详情
    if (report.routeErrors && report.routeErrors.length > 0) {
      console.log(chalk.yellow('\n🚨 路由错误详情:'));
      for (const error of report.routeErrors) {
        console.log(chalk.yellow(`   - [${error.phase}] ${error.message.substring(0, 150)}${error.message.length > 150 ? '...' : ''}`));
        if (this.options.verbose && error.timestamp) {
          console.log(chalk.gray(`     时间: ${error.timestamp}`));
        }
      }
      console.log(chalk.gray('\n   💡 提示: 路由错误通常需要修复路由配置文件'));
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }

      if (this.devServer && !this.devServer.killed) {
        this.devServer.kill('SIGTERM');

        // 等待进程结束
        await new Promise((resolve) => {
          this.devServer.on('exit', resolve);
          setTimeout(resolve, 5000); // 5秒超时
        });
      }

      if (this.runtimeErrorHandler) {
        // 清理运行时错误处理器
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  清理资源时出错: ${error.message}`));
      }
    }
  }

  /**
   * 尝试修复页面错误
   */
  async attemptPageErrorFix(route, errors) {
    try {
      if (!this.buildFixAgent) {
        return { success: false, error: 'BuildFixAgent 未初始化' };
      }

      // 过滤出可以修复的错误（排除登录、权限、网络等非代码错误）
      const fixableErrors = this.errorAnalyzer.filterFixableErrors(errors);

      if (fixableErrors.length === 0) {
        return {
          success: false,
          error: '没有可修复的代码错误',
          filteredCount: errors.length - fixableErrors.length
        };
      }

      // 构建错误上下文信息
      const errorContext = this.buildPageErrorContext(route, fixableErrors);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 分析页面错误: ${route.path}`));
        console.log(chalk.gray(`    总错误数量: ${errors.length}, 可修复错误: ${fixableErrors.length}`));
      }

      // 分析错误并确定需要修复的文件
      const analysisResult = await this.buildFixAgent.errorAnalyzer.analyzeBuildErrors(errorContext.buildOutput, 1);

      if (!analysisResult.success || !analysisResult.filesToFix || analysisResult.filesToFix.length === 0) {
        return {
          success: false,
          error: '无法确定需要修复的文件',
          analysisResult
        };
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    📁 需要修复的文件: ${analysisResult.filesToFix.join(', ')}`));
      }

      // 执行文件修复
      const fixResult = await this.buildFixAgent.fixFiles(
        analysisResult.filesToFix,
        errorContext.buildOutput,
        1
      );

      return {
        success: fixResult.success,
        filesModified: fixResult.filesModified || 0,
        totalFiles: fixResult.totalFiles || 0,
        errors: fixResult.errors,
        analysisResult,
        fixResult
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * 构建页面错误上下文信息
   */
  buildPageErrorContext(route, errors) {
    // 使用路由解析器推断可能的组件文件路径
    let suggestedFiles = [];
    let routeComponentInfo = {};
    if (this.routeParser) {
      const errorMessages = errors.map(error => {
        if (typeof error === 'string') {
          return error;
        } else if (error.message) {
          return `${error.type || 'Error'}: ${error.message}`;
        } else {
          return JSON.stringify(error);
        }
      });

      const errorMessage = errorMessages.join(' ');
      suggestedFiles = this.routeParser.inferComponentPaths(route.path, errorMessage);

      const directComponent = this.routeParser.getComponentPathByRoute(route.path);
      if (directComponent) {
        routeComponentInfo.directMapping = directComponent;
      }

      const allMappings = this.routeParser.getRouteComponentMap();
      routeComponentInfo.relatedMappings = this.findRelatedRouteMappings(route.path, allMappings);
    }

    const routeInfo = this.buildRouteContextInfo(route);

    const errorContext = {
      route,
      errors,
      timestamp: new Date().toISOString(),
      suggestedFiles,
      routeComponentInfo,
      routeInfo
    };

    const buildOutput = this.buildErrorAnalyzer.formatRuntimeErrorAsBuildOutput(errorContext);

    return {
      buildOutput,
      route,
      errors,
      errorCount: errors.length,
      timestamp: new Date().toISOString(),
      suggestedFiles, // 添加推荐的文件列表
      routeComponentInfo, // 添加路由组件信息
      routeInfo // 添加路由上下文信息
    };
  }

  /**
   * 构建路由上下文信息
   */
  buildRouteContextInfo(route) {
    const info = [];

    info.push(`路径: ${route.path}`);
    if (route.name) info.push(`名称: ${route.name}`);
    if (route.component) {
      if (typeof route.component === 'object') {
        info.push(`组件类型: ${route.component.type || 'unknown'}`);
        if (route.component.source) {
          info.push(`组件源: ${route.component.source}`);
        }
      } else {
        info.push(`组件: ${route.component}`);
      }
    }
    if (route.meta && Object.keys(route.meta).length > 0) {
      info.push(`元信息: ${JSON.stringify(route.meta)}`);
    }
    if (route.children && route.children.length > 0) {
      info.push(`子路由数量: ${route.children.length}`);
    }

    return info.join('\n');
  }

  /**
   * 查找相关的路由映射
   */
  findRelatedRouteMappings(currentPath, allMappings) {
    const related = [];
    const pathSegments = currentPath.split('/').filter(Boolean);

    for (const [routePath, component] of allMappings) {
      if (routePath === currentPath) continue;

      const routeSegments = routePath.split('/').filter(Boolean);

      // 查找父路由或同级路由
      if (this.isRelatedRoute(pathSegments, routeSegments)) {
        related.push({ route: routePath, component });
      }
    }

    return related.slice(0, 5); // 限制数量
  }

  /**
   * 判断是否是相关路由
   */
  isRelatedRoute(currentSegments, routeSegments) {
    // 父路由关系
    if (routeSegments.length < currentSegments.length) {
      return routeSegments.every((segment, index) => segment === currentSegments[index]);
    }

    // 同级路由关系
    if (routeSegments.length === currentSegments.length) {
      const commonSegments = routeSegments.filter((segment, index) => segment === currentSegments[index]);
      return commonSegments.length >= Math.max(1, routeSegments.length - 1);
    }

    return false;
  }

  /**
   * 检查页面错误（用于重新验证）
   */
  async checkPageErrors(page) {
    const errors = [];

    try {
      // 检查页面错误数据
      const errorData = await page.evaluate(() => {
        return {
          pageErrors: window.__pageErrors || [],
          vueErrors: window.__vueErrors || [],
          consoleErrors: window.__consoleErrors || []
        };
      });

      // 收集所有错误
      errors.push(...errorData.pageErrors.map(e => ({ type: 'page-error', message: e.message })));
      errors.push(...errorData.vueErrors.map(e => ({ type: 'vue-error', message: e.message })));
      errors.push(...errorData.consoleErrors.filter(e => e.level === 'error').map(e => ({ type: 'console-error', message: e.message })));

      // 检查DOM中的错误元素
      const domErrors = await page.evaluate(() => {
        const errorElements = document.querySelectorAll([
          '[class*="error"]',
          '[class*="Error"]',
          '.error-message',
          '.runtime-error'
        ].join(','));

        const errors = [];
        for (const element of errorElements) {
          const text = element.textContent || element.innerText;
          if (text && text.length > 10 && (
            text.includes('Error') ||
            text.includes('Cannot read properties') ||
            text.includes('TypeError')
          )) {
            errors.push({
              type: 'dom-error',
              message: text.substring(0, 200)
            });
          }
        }
        return errors;
      });

      errors.push(...domErrors);

    } catch (error) {
      errors.push({
        type: 'check-error',
        message: `检查页面错误时出错: ${error.message}`
      });
    }

    return errors;
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取验证结果
   */
  getResults() {
    return this.validationResults;
  }

  /**
   * 保存报告到文件
   */
  async saveReport(outputPath) {
    const report = this.generateReport();

    // 生成 Markdown 报告
    const markdown = this.generateMarkdownReport(report);

    await fs.writeFile(outputPath, markdown, 'utf8');
    console.log(chalk.green(`📄 验证报告已保存: ${outputPath}`));
  }

  /**
   * 生成 Markdown 格式的报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 页面验证报告\n\n`;
    markdown += `生成时间: ${report.timestamp}\n\n`;

    markdown += `## 摘要\n\n`;
    markdown += `- 总页面数: ${report.summary.total}\n`;
    markdown += `- 成功: ${report.summary.successful}\n`;
    markdown += `- 失败: ${report.summary.failed}\n`;
    markdown += `- 成功率: ${report.summary.successRate}%\n\n`;

    if (report.failedPages.length > 0) {
      markdown += `## 失败的页面\n\n`;
      for (const failed of report.failedPages) {
        markdown += `### ${failed.route.path}\n\n`;
        markdown += `- URL: ${failed.url}\n`;
        markdown += `- 加载时间: ${failed.loadTime}ms\n`;

        if (failed.errors.length > 0) {
          markdown += `- 错误:\n`;
          const formattedErrors = this.errorAnalyzer.formatErrorsForDisplay(failed.errors);
          for (const errorInfo of formattedErrors) {
            markdown += `  - **${errorInfo.type}**: ${errorInfo.summary}\n`;
            if (errorInfo.details) {
              markdown += `    \`\`\`\n    ${errorInfo.details}\n    \`\`\`\n`;
            }
          }

          if (failed.errors.length > formattedErrors.length) {
            markdown += `  - ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误\n`;
          }
        }

        if (failed.warnings.length > 0) {
          markdown += `- 警告:\n`;
          for (const warning of failed.warnings) {
            markdown += `  - ${warning}\n`;
          }
        }

        markdown += `\n`;
      }
    }

    // 添加 FAQ 分析结果
    if (report.faqAnalysis && report.faqAnalysis.suggestions.length > 0) {
      markdown += `## 💡 常见问题解决建议\n\n`;
      markdown += `${report.faqAnalysis.summary}\n\n`;

      for (const suggestion of report.faqAnalysis.suggestions) {
        const count = report.faqAnalysis.errorCounts.get(suggestion.solution) || 0;
        markdown += `### ${suggestion.solution} (${count} 个相关错误)\n\n`;

        if (suggestion.summary) {
          if (suggestion.summary.cause) {
            markdown += `**根本原因**: ${suggestion.summary.cause}\n\n`;
          }

          if (suggestion.summary.steps.length > 0) {
            markdown += `**解决步骤**:\n`;
            for (const step of suggestion.summary.steps) {
              markdown += `1. ${step}\n`;
            }
            markdown += `\n`;
          }
        }
      }

      markdown += `> 📖 详细解决方案请参考: [docs/runtime-faq-summary.md](docs/runtime-faq-summary.md)\n\n`;
    }

    markdown += `## 详细结果\n\n`;
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌';
      markdown += `- ${status} ${result.route.path} (${result.loadTime}ms)\n`;
    }

    return markdown;
  }
}

module.exports = PageValidator;
