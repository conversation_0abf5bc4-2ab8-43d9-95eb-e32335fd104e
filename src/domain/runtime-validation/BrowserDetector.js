const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn, exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

/**
 * BrowserDetector - 浏览器检测和管理工具
 * 
 * 功能：
 * 1. 检测系统中可用的浏览器
 * 2. 自动下载和安装 Puppeteer 浏览器
 * 3. 提供浏览器兼容性检测
 * 4. 支持多种浏览器引擎
 */
class BrowserDetector {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      preferredBrowsers: ['chrome', 'chromium', 'edge'],
      downloadTimeout: 300000, // 5分钟下载超时
      ...options
    };

    this.detectedBrowsers = [];
    this.selectedBrowser = null;
  }

  /**
   * 检测所有可用的浏览器
   */
  async detectAvailableBrowsers() {
    console.log(chalk.blue('🔍 检测可用浏览器...'));

    const platform = process.platform;
    this.detectedBrowsers = [];

    // 检测 Puppeteer 浏览器
    await this.detectPuppeteerBrowsers();

    // 检测系统浏览器
    await this.detectSystemBrowsers(platform);

    if (this.options.verbose) {
      this.printDetectedBrowsers();
    }

    return this.detectedBrowsers;
  }

  /**
   * 检测 Puppeteer 浏览器
   */
  async detectPuppeteerBrowsers() {
    try {
      const puppeteer = require('puppeteer');
      const browserFetcher = puppeteer.createBrowserFetcher();
      const localRevisions = await browserFetcher.localRevisions();

      for (const revision of localRevisions) {
        const revisionInfo = browserFetcher.revisionInfo(revision);
        if (await fs.pathExists(revisionInfo.executablePath)) {
          this.detectedBrowsers.push({
            name: 'Chrome (Puppeteer)',
            type: 'puppeteer',
            executablePath: revisionInfo.executablePath,
            version: revision,
            available: true
          });
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  Puppeteer 浏览器检测失败: ${error.message}`));
      }
    }
  }

  /**
   * 检测系统浏览器
   */
  async detectSystemBrowsers(platform) {
    const browserConfigs = this.getBrowserConfigs(platform);

    for (const config of browserConfigs) {
      try {
        const isAvailable = await this.checkBrowserAvailability(config);
        if (isAvailable) {
          this.detectedBrowsers.push({
            ...config,
            available: true
          });
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`⚠️  检测 ${config.name} 失败: ${error.message}`));
        }
      }
    }
  }

  /**
   * 获取不同平台的浏览器配置
   */
  getBrowserConfigs(platform) {
    const configs = {
      darwin: [
        {
          name: 'Google Chrome',
          type: 'chrome',
          executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
          command: 'google-chrome'
        },
        {
          name: 'Chromium',
          type: 'chromium',
          executablePath: '/Applications/Chromium.app/Contents/MacOS/Chromium',
          command: 'chromium'
        },
        {
          name: 'Microsoft Edge',
          type: 'edge',
          executablePath: '/Applications/Microsoft Edge.app/Contents/MacOS/Microsoft Edge',
          command: 'microsoft-edge'
        }
      ],
      win32: [
        {
          name: 'Google Chrome',
          type: 'chrome',
          executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
          command: 'chrome'
        },
        {
          name: 'Google Chrome (x86)',
          type: 'chrome',
          executablePath: 'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
          command: 'chrome'
        },
        {
          name: 'Microsoft Edge',
          type: 'edge',
          executablePath: 'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
          command: 'msedge'
        }
      ],
      linux: [
        {
          name: 'Google Chrome',
          type: 'chrome',
          executablePath: '/usr/bin/google-chrome',
          command: 'google-chrome'
        },
        {
          name: 'Chromium',
          type: 'chromium',
          executablePath: '/usr/bin/chromium-browser',
          command: 'chromium-browser'
        },
        {
          name: 'Chromium (snap)',
          type: 'chromium',
          executablePath: '/snap/bin/chromium',
          command: 'chromium'
        }
      ]
    };

    return configs[platform] || configs.linux;
  }

  /**
   * 检查浏览器是否可用
   */
  async checkBrowserAvailability(config) {
    // 首先检查文件是否存在
    if (await fs.pathExists(config.executablePath)) {
      try {
        // 尝试获取版本信息
        const version = await this.getBrowserVersion(config);
        config.version = version;
        return true;
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`⚠️  ${config.name} 文件存在但无法执行: ${error.message}`));
        }
        return false;
      }
    }

    // 如果文件不存在，尝试通过命令检查
    try {
      await execAsync(`which ${config.command}`, { timeout: 5000 });
      const version = await this.getBrowserVersion(config);
      config.version = version;
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取浏览器版本
   */
  async getBrowserVersion(config) {
    try {
      const versionCommand = `"${config.executablePath}" --version`;
      const { stdout } = await execAsync(versionCommand, { timeout: 10000 });
      return stdout.trim();
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * 选择最佳浏览器
   */
  selectBestBrowser() {
    if (this.detectedBrowsers.length === 0) {
      return null;
    }

    // 优先选择 Puppeteer 浏览器
    const puppeteerBrowser = this.detectedBrowsers.find(b => b.type === 'puppeteer');
    if (puppeteerBrowser) {
      this.selectedBrowser = puppeteerBrowser;
      return puppeteerBrowser;
    }

    // 按优先级选择系统浏览器
    for (const preferredType of this.options.preferredBrowsers) {
      const browser = this.detectedBrowsers.find(b => b.type === preferredType);
      if (browser) {
        this.selectedBrowser = browser;
        return browser;
      }
    }

    // 如果没有找到优先浏览器，选择第一个可用的
    this.selectedBrowser = this.detectedBrowsers[0];
    return this.selectedBrowser;
  }

  /**
   * 确保有可用的浏览器
   */
  async ensureBrowser() {
    await this.detectAvailableBrowsers();

    if (this.detectedBrowsers.length === 0) {
      console.log(chalk.yellow('⚠️  未找到可用浏览器，尝试下载 Chrome...'));
      const success = await this.downloadPuppeteerBrowser();
      
      if (success) {
        await this.detectPuppeteerBrowsers();
      } else {
        throw new Error('无法找到或下载可用的浏览器');
      }
    }

    const selectedBrowser = this.selectBestBrowser();
    if (!selectedBrowser) {
      throw new Error('无法选择可用的浏览器');
    }

    console.log(chalk.green(`✅ 选择浏览器: ${selectedBrowser.name}`));
    return selectedBrowser;
  }

  /**
   * 下载 Puppeteer 浏览器
   */
  async downloadPuppeteerBrowser() {
    try {
      const puppeteer = require('puppeteer');
      const browserFetcher = puppeteer.createBrowserFetcher();

      console.log(chalk.blue('📥 正在下载 Chrome 浏览器...'));
      
      const revisionInfo = await browserFetcher.download(
        puppeteer.PUPPETEER_REVISIONS.chromium,
        (downloadedBytes, totalBytes) => {
          if (this.options.verbose) {
            const percent = ((downloadedBytes / totalBytes) * 100).toFixed(1);
            process.stdout.write(`\r   下载进度: ${percent}%`);
          }
        }
      );

      if (this.options.verbose) {
        console.log(''); // 换行
      }

      console.log(chalk.green(`✅ Chrome 浏览器下载完成: ${revisionInfo.executablePath}`));
      return true;
    } catch (error) {
      console.log(chalk.red(`❌ Chrome 浏览器下载失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 打印检测到的浏览器
   */
  printDetectedBrowsers() {
    console.log(chalk.blue('\n🌐 检测到的浏览器:'));
    
    if (this.detectedBrowsers.length === 0) {
      console.log(chalk.yellow('   未找到可用浏览器'));
      return;
    }

    for (const browser of this.detectedBrowsers) {
      const status = browser.available ? '✅' : '❌';
      console.log(chalk.gray(`   ${status} ${browser.name} (${browser.version || 'Unknown'})`));
    }
  }

  /**
   * 获取检测到的浏览器列表
   */
  getDetectedBrowsers() {
    return this.detectedBrowsers;
  }

  /**
   * 获取选择的浏览器
   */
  getSelectedBrowser() {
    return this.selectedBrowser;
  }
}

module.exports = BrowserDetector;
