const path = require('path');
const chalk = require('chalk');
const MigrationPhase = require('./MigrationPhase');
const RuntimeErrorHandler = require('./RuntimeErrorHandler');
const BuildExecutor = require('../build-fix/BuildExecutor');

/**
 * RuntimeErrorRepairPhase - 运行时错误修复阶段
 *
 * 功能：
 * 1. 启动开发服务器并注入错误监控代码
 * 2. 监听运行时错误并自动修复
 * 3. 提供错误统计和修复报告
 * 4. 支持热重载和实时修复
 */
class RuntimeErrorRepairPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('运行时错误修复', projectPath, options);

    this.projectPath = projectPath;
    this.options = {
      port: 3000,
      timeout: 30000, // 30秒监控时间
      maxErrors: 50,
      autoFix: true,
      verbose: false,
      ...options
    };

    // 运行时错误处理器
    this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
      port: this.options.port,
      maxErrors: this.options.maxErrors,
      autoFix: this.options.autoFix,
      verbose: this.options.verbose
    });

    // 构建执行器
    this.buildExecutor = new BuildExecutor(projectPath, {
      devCommand: this.options.devCommand || 'npm run dev',
      devTimeout: this.options.timeout,
      verbose: this.options.verbose
    });

    // 错误统计
    this.stats = {
      totalErrors: 0,
      fixedErrors: 0,
      pendingErrors: 0,
      startTime: null,
      endTime: null
    };
  }

  /**
   * 执行运行时错误修复阶段
   */
  async execute() {
    try {
      this.stats.startTime = Date.now();

      console.log(chalk.blue('🚀 启动运行时错误监控...'));

      // 1. 检查项目配置
      await this.checkProjectConfiguration();

      // 2. 注入错误监控代码
      await this.injectErrorMonitoring();

      // 3. 启动开发服务器
      const devServer = await this.startDevServer();

      // 4. 监控运行时错误
      const monitoringResult = await this.monitorRuntimeErrors();

      // 5. 停止开发服务器
      await this.stopDevServer(devServer);

      // 6. 生成修复报告
      const report = this.generateReport();

      this.stats.endTime = Date.now();

      return {
        success: true,
        stats: this.stats,
        report,
        ...monitoringResult
      };

    } catch (error) {
      console.error(chalk.red(`运行时错误修复失败: ${error.message}`));
      return {
        success: false,
        error: error.message,
        stats: this.stats
      };
    }
  }

  /**
   * 检查项目配置
   */
  async checkProjectConfiguration() {
    console.log(chalk.gray('  📋 检查项目配置...'));

    // 检查package.json中的dev命令
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    const packageJson = require(packageJsonPath);

    if (!packageJson.scripts || !packageJson.scripts.dev) {
      throw new Error('项目缺少 dev 启动脚本');
    }

    // 检查Vue版本
    const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
    if (!vueVersion) {
      throw new Error('项目中未找到Vue依赖');
    }

    console.log(chalk.gray(`  ✅ 项目配置检查完成 (Vue ${vueVersion})`));
  }

  /**
   * 注入错误监控代码
   */
  async injectErrorMonitoring() {
    console.log(chalk.gray('  🔧 注入运行时错误监控代码...'));

    const fs = require('fs-extra');

    // 使用监控服务器端口生成注入脚本
    const monitoringPort = this.options.port + 1;
    const injectionScript = this.runtimeErrorHandler.generateInjectionCode(monitoringPort);

    // 1. 创建临时注入文件
    const injectionFilePath = path.join(this.projectPath, 'runtime-error-injection.js');
    await fs.writeFile(injectionFilePath, injectionScript, 'utf8');

    // 2. 尝试注入到项目的入口文件
    await this.injectToEntryFiles(injectionScript);

    // 3. 尝试注入到HTML文件
    await this.injectToHTMLFiles(injectionScript);

    console.log(chalk.gray(`  📝 错误监控脚本已生成: ${injectionFilePath}`));
    console.log(chalk.gray(`  🔍 错误监控端点: http://localhost:${monitoringPort}/__runtime_errors__`));
    console.log(chalk.gray('  ✅ 错误监控代码注入完成'));

    return injectionFilePath;
  }

  /**
   * 注入到项目的入口文件
   */
  async injectToEntryFiles(injectionScript) {
    const fs = require('fs-extra');

    // 常见的入口文件路径
    const entryFiles = [
      path.join(this.projectPath, 'src/main.js'),
      path.join(this.projectPath, 'src/main.ts'),
      path.join(this.projectPath, 'src/index.js'),
      path.join(this.projectPath, 'src/index.ts'),
      path.join(this.projectPath, 'main.js'),
      path.join(this.projectPath, 'index.js')
    ];

    for (const entryFile of entryFiles) {
      if (await fs.pathExists(entryFile)) {
        try {
          const content = await fs.readFile(entryFile, 'utf8');

          // 检查是否已经注入过
          if (content.includes('RUNTIME_ERROR_MONITORING_INJECTED')) {
            console.log(chalk.gray(`    ⚠️  ${entryFile} 已注入过错误监控代码`));
            continue;
          }

          // 在文件开头注入错误监控代码
          const injectedContent = `// RUNTIME_ERROR_MONITORING_INJECTED
${injectionScript}

${content}`;

          // 备份原文件
          await fs.copy(entryFile, `${entryFile}.runtime-backup`);

          // 写入注入后的内容
          await fs.writeFile(entryFile, injectedContent, 'utf8');

          console.log(chalk.gray(`    ✅ 已注入错误监控代码到: ${entryFile}`));

          // 记录注入的文件，用于后续清理
          this.injectedFiles = this.injectedFiles || [];
          this.injectedFiles.push(entryFile);

          return true;
        } catch (error) {
          console.log(chalk.yellow(`    ⚠️  注入到 ${entryFile} 失败: ${error.message}`));
        }
      }
    }

    return false;
  }

  /**
   * 注入到HTML文件
   */
  async injectToHTMLFiles(injectionScript) {
    const fs = require('fs-extra');

    // 常见的HTML文件路径
    const htmlFiles = [
      path.join(this.projectPath, 'public/index.html'),
      path.join(this.projectPath, 'index.html'),
      path.join(this.projectPath, 'src/index.html')
    ];

    for (const htmlFile of htmlFiles) {
      if (await fs.pathExists(htmlFile)) {
        try {
          const content = await fs.readFile(htmlFile, 'utf8');

          // 检查是否已经注入过
          if (content.includes('RUNTIME_ERROR_MONITORING_INJECTED')) {
            console.log(chalk.gray(`    ⚠️  ${htmlFile} 已注入过错误监控代码`));
            continue;
          }

          // 在head标签中注入错误监控代码
          const scriptTag = `  <!-- RUNTIME_ERROR_MONITORING_INJECTED -->
  <script>
${injectionScript}
  </script>`;

          let injectedContent;
          if (content.includes('</head>')) {
            injectedContent = content.replace('</head>', `${scriptTag}\n</head>`);
          } else if (content.includes('<body>')) {
            injectedContent = content.replace('<body>', `<body>\n${scriptTag}`);
          } else {
            // 如果没有找到合适的位置，在文档开头添加
            injectedContent = `${scriptTag}\n${content}`;
          }

          // 备份原文件
          await fs.copy(htmlFile, `${htmlFile}.runtime-backup`);

          // 写入注入后的内容
          await fs.writeFile(htmlFile, injectedContent, 'utf8');

          console.log(chalk.gray(`    ✅ 已注入错误监控代码到: ${htmlFile}`));

          // 记录注入的文件，用于后续清理
          this.injectedFiles = this.injectedFiles || [];
          this.injectedFiles.push(htmlFile);

          return true;
        } catch (error) {
          console.log(chalk.yellow(`    ⚠️  注入到 ${htmlFile} 失败: ${error.message}`));
        }
      }
    }

    return false;
  }

  /**
   * 启动开发服务器并动态检测端口
   */
  async startDevServer() {
    console.log(chalk.gray('  🌐 启动开发服务器...'));

    try {
      // 设置环境变量，让开发服务器知道要注入错误监控
      process.env.RUNTIME_ERROR_MONITORING = 'true';
      process.env.RUNTIME_ERROR_ENDPOINT = this.runtimeErrorHandler.options.errorEndpoint;

      // 启动开发服务器（非阻塞）
      const devProcess = await this.buildExecutor.startDevServer({
        port: this.options.port
      });

      // 等待服务器启动并检测实际端口
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 动态检测实际运行的端口
      const actualPort = await this.detectActualPort();
      if (actualPort && actualPort !== this.options.port) {
        console.log(chalk.yellow(`  ⚠️  检测到实际端口: ${actualPort}，更新配置`));
        this.options.port = actualPort;
      }

      // 启动独立的错误监控服务器（因为无法直接修改webpack配置）
      await this.startErrorMonitoringServer();

      console.log(chalk.green(`  ✅ 开发服务器已启动 (端口: ${this.options.port})`));

      return devProcess;

    } catch (error) {
      throw new Error(`启动开发服务器失败: ${error.message}`);
    }
  }

  /**
   * 检测实际的开发服务器端口
   */
  async detectActualPort() {
    const possiblePorts = [9527, 8080, 3000, 4000, 5000, 8000];

    for (const port of possiblePorts) {
      try {
        const axios = require('axios');
        await axios.get(`http://localhost:${port}`, { timeout: 1000 });
        console.log(chalk.gray(`  🔍 检测到开发服务器运行在端口: ${port}`));
        return port;
      } catch (error) {
        // 端口不可用，继续检测下一个
      }
    }

    return null;
  }

  /**
   * 启动独立的错误监控服务器
   */
  async startErrorMonitoringServer() {
    const express = require('express');

    const app = express();
    const monitoringPort = this.options.port + 1; // 使用下一个端口

    // 启用CORS和JSON解析
    app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });
    app.use(express.json());

    // 添加运行时错误处理路由
    app.use(this.runtimeErrorHandler.getRouter());

    // 启动监控服务器
    return new Promise((resolve, reject) => {
      this.monitoringServer = app.listen(monitoringPort, (err) => {
        if (err) {
          reject(new Error(`错误监控服务器启动失败: ${err.message}`));
        } else {
          console.log(chalk.gray(`  🔍 错误监控服务器已启动 (端口: ${monitoringPort})`));
          resolve();
        }
      });
    });
  }

  /**
   * 监控运行时错误
   */
  async monitorRuntimeErrors() {
    console.log(chalk.blue(`  👀 开始监控运行时错误 (${this.options.timeout / 1000}秒)...`));

    const startTime = Date.now();
    const endTime = startTime + this.options.timeout;

    return new Promise((resolve) => {
      const checkInterval = setInterval(async () => {
        const now = Date.now();
        const stats = this.runtimeErrorHandler.getErrorStats();

        // 更新统计信息
        this.stats.totalErrors = stats.totalErrors;
        this.stats.fixedErrors = stats.fixedErrors;
        this.stats.pendingErrors = stats.pendingErrors;

        // 输出进度信息
        if (this.options.verbose && stats.totalErrors > 0) {
          const elapsed = Math.floor((now - startTime) / 1000);
          console.log(chalk.gray(
            `    [${elapsed}s] 错误: ${stats.totalErrors}, 已修复: ${stats.fixedErrors}, 待处理: ${stats.pendingErrors}`
          ));
        }

        // 检查是否超时
        if (now >= endTime) {
          clearInterval(checkInterval);
          await this.handleMonitoringTimeout(startTime, resolve);
        }
      }, 1000); // 每秒检查一次
    });
  }

  /**
   * 处理监控超时，等待正在进行的修复完成
   */
  async handleMonitoringTimeout(startTime, resolve) {
    console.log(chalk.blue('  ⏰ 监控时间结束'));

    // 检查是否有正在进行的修复
    const pendingFixes = this.runtimeErrorHandler.getPendingFixes();

    if (pendingFixes.length > 0) {
      console.log(chalk.yellow(`  🔄 等待 ${pendingFixes.length} 个正在进行的修复完成...`));

      // 等待所有正在进行的修复完成，最多等待额外的30秒
      const maxWaitTime = 30000; // 30秒
      const waitStartTime = Date.now();

      while (Date.now() - waitStartTime < maxWaitTime) {
        const currentPendingFixes = this.runtimeErrorHandler.getPendingFixes();

        if (currentPendingFixes.length === 0) {
          console.log(chalk.green('  ✅ 所有修复已完成'));
          break;
        }

        // 每2秒检查一次
        await new Promise(resolve => setTimeout(resolve, 2000));

        const elapsed = Math.floor((Date.now() - waitStartTime) / 1000);
        console.log(chalk.gray(`    等待修复完成... (${elapsed}s, 剩余: ${currentPendingFixes.length})`));
      }

      // 如果超时仍有未完成的修复
      const finalPendingFixes = this.runtimeErrorHandler.getPendingFixes();
      if (finalPendingFixes.length > 0) {
        console.log(chalk.yellow(`  ⚠️  超时后仍有 ${finalPendingFixes.length} 个修复未完成`));
      }
    }

    // 获取最终统计信息
    const finalStats = this.runtimeErrorHandler.getErrorStats();
    this.stats.totalErrors = finalStats.totalErrors;
    this.stats.fixedErrors = finalStats.fixedErrors;
    this.stats.pendingErrors = finalStats.pendingErrors;

    const now = Date.now();
    resolve({
      monitoringCompleted: true,
      duration: now - startTime,
      errorsDetected: finalStats.totalErrors,
      errorsFixed: finalStats.fixedErrors
    });
  }

  /**
   * 停止开发服务器
   */
  async stopDevServer(devProcess) {
    console.log(chalk.gray('  🛑 停止开发服务器...'));

    // 清理注入的错误监控代码
    await this.cleanupInjectedCode();

    // 停止错误监控服务器
    if (this.monitoringServer) {
      this.monitoringServer.close();
      console.log(chalk.gray('  ✅ 错误监控服务器已停止'));
    }

    // 停止开发服务器进程
    if (devProcess && devProcess.kill) {
      devProcess.kill('SIGTERM');

      // 等待进程结束
      await new Promise((resolve) => {
        devProcess.on('exit', resolve);
        setTimeout(resolve, 5000); // 5秒超时
      });
    }

    console.log(chalk.gray('  ✅ 开发服务器已停止'));
  }

  /**
   * 清理注入的错误监控代码
   */
  async cleanupInjectedCode() {
    const fs = require('fs-extra');

    if (!this.injectedFiles || this.injectedFiles.length === 0) {
      return;
    }

    console.log(chalk.gray('  🧹 清理注入的错误监控代码...'));

    for (const filePath of this.injectedFiles) {
      try {
        const backupPath = `${filePath}.runtime-backup`;

        if (await fs.pathExists(backupPath)) {
          // 恢复备份文件
          await fs.copy(backupPath, filePath);
          await fs.remove(backupPath);
          console.log(chalk.gray(`    ✅ 已恢复: ${filePath}`));
        }
      } catch (error) {
        console.log(chalk.yellow(`    ⚠️  恢复文件失败 ${filePath}: ${error.message}`));
      }
    }

    // 清理临时注入文件
    try {
      const injectionFilePath = path.join(this.projectPath, 'runtime-error-injection.js');
      if (await fs.pathExists(injectionFilePath)) {
        await fs.remove(injectionFilePath);
        console.log(chalk.gray(`    ✅ 已删除临时文件: runtime-error-injection.js`));
      }
    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  删除临时文件失败: ${error.message}`));
    }

    this.injectedFiles = [];
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    const duration = Math.max(0, this.stats.endTime - this.stats.startTime);
    const fixRate = this.stats.totalErrors > 0
      ? (this.stats.fixedErrors / this.stats.totalErrors * 100).toFixed(1)
      : 0;

    const report = {
      summary: {
        duration: `${Math.floor(duration / 1000)}秒`,
        totalErrors: this.stats.totalErrors,
        fixedErrors: this.stats.fixedErrors,
        pendingErrors: this.stats.pendingErrors,
        fixRate: `${fixRate}%`
      },
      details: {
        monitoringStarted: new Date(this.stats.startTime).toISOString(),
        monitoringEnded: new Date(this.stats.endTime).toISOString(),
        autoFixEnabled: this.options.autoFix
      }
    };

    // 输出报告
    console.log(chalk.blue('\n📊 运行时错误修复报告:'));
    console.log(chalk.gray(`  监控时长: ${report.summary.duration}`));
    console.log(chalk.gray(`  检测错误: ${report.summary.totalErrors} 个`));
    console.log(chalk.gray(`  修复错误: ${report.summary.fixedErrors} 个`));
    console.log(chalk.gray(`  待处理错误: ${report.summary.pendingErrors} 个`));
    console.log(chalk.gray(`  修复成功率: ${report.summary.fixRate}`));

    return report;
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['构建时错误修复']; // 依赖构建时修复阶段
  }

  /**
   * 获取关键依赖
   */
  getCriticalDependencies() {
    return []; // 运行时修复不是关键依赖
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError() {
    // 运行时错误修复失败不应该阻止后续阶段
    return false;
  }
}

module.exports = RuntimeErrorRepairPhase;
