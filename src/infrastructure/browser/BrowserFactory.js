const { PuppeteerAdapter } = require('./PuppeteerAdapter');
const { PlaywrightAdapter } = require('./PlaywrightAdapter');

/**
 * 浏览器工厂类
 * 负责创建和管理不同类型的浏览器自动化适配器
 */
class BrowserFactory {
  constructor() {
    this.adapters = new Map();
    this.defaultAdapter = 'puppeteer';
    
    // 注册默认适配器
    this.registerAdapter('puppeteer', PuppeteerAdapter);
    this.registerAdapter('playwright-chromium', () => new PlaywrightAdapter('chromium'));
    this.registerAdapter('playwright-firefox', () => new PlaywrightAdapter('firefox'));
    this.registerAdapter('playwright-webkit', () => new PlaywrightAdapter('webkit'));
  }

  /**
   * 注册浏览器适配器
   * @param {string} name 适配器名称
   * @param {Function|Class} adapterClass 适配器类或工厂函数
   */
  registerAdapter(name, adapterClass) {
    this.adapters.set(name, adapterClass);
  }

  /**
   * 创建浏览器自动化实例
   * @param {string} type 浏览器类型 ('puppeteer' | 'playwright-chromium' | 'playwright-firefox' | 'playwright-webkit')
   * @returns {IBrowserAutomation} 浏览器自动化实例
   */
  createBrowserAutomation(type = null) {
    const adapterType = type || this.defaultAdapter;
    
    if (!this.adapters.has(adapterType)) {
      throw new Error(`Unsupported browser automation type: ${adapterType}`);
    }

    const AdapterClass = this.adapters.get(adapterType);
    
    // 如果是函数，直接调用；如果是类，使用 new 创建实例
    if (typeof AdapterClass === 'function' && AdapterClass.prototype) {
      return new AdapterClass();
    } else {
      return AdapterClass();
    }
  }

  /**
   * 获取所有可用的适配器类型
   * @returns {Array<string>} 适配器类型列表
   */
  getAvailableTypes() {
    return Array.from(this.adapters.keys());
  }

  /**
   * 设置默认适配器类型
   * @param {string} type 适配器类型
   */
  setDefaultAdapter(type) {
    if (!this.adapters.has(type)) {
      throw new Error(`Unknown adapter type: ${type}`);
    }
    this.defaultAdapter = type;
  }

  /**
   * 获取默认适配器类型
   * @returns {string} 默认适配器类型
   */
  getDefaultAdapter() {
    return this.defaultAdapter;
  }

  /**
   * 检测系统中可用的浏览器
   * @returns {Promise<Array>} 可用浏览器列表
   */
  async detectAvailableBrowsers() {
    const allBrowsers = [];
    
    for (const [type, AdapterClass] of this.adapters) {
      try {
        let adapter;
        if (typeof AdapterClass === 'function' && AdapterClass.prototype) {
          adapter = new AdapterClass();
        } else {
          adapter = AdapterClass();
        }
        
        const browsers = await adapter.detectBrowsers();
        allBrowsers.push(...browsers.map(browser => ({
          ...browser,
          adapterType: type
        })));
      } catch (error) {
        // 忽略检测失败的适配器
        console.warn(`Failed to detect browsers for ${type}:`, error.message);
      }
    }
    
    return allBrowsers;
  }

  /**
   * 自动选择最佳的浏览器适配器
   * @param {Array<string>} preferredTypes 优先选择的类型列表
   * @returns {Promise<string>} 选择的适配器类型
   */
  async selectBestAdapter(preferredTypes = ['puppeteer', 'playwright-chromium']) {
    const availableBrowsers = await this.detectAvailableBrowsers();
    
    if (availableBrowsers.length === 0) {
      throw new Error('No browsers available');
    }

    // 按优先级选择
    for (const preferredType of preferredTypes) {
      const browser = availableBrowsers.find(b => b.adapterType === preferredType);
      if (browser) {
        return preferredType;
      }
    }

    // 如果没有找到优先的类型，返回第一个可用的
    return availableBrowsers[0].adapterType;
  }

  /**
   * 创建配置好的浏览器自动化实例
   * @param {Object} options 配置选项
   * @param {string} options.type 浏览器类型
   * @param {Array<string>} options.preferredTypes 优先选择的类型列表
   * @param {boolean} options.autoSelect 是否自动选择最佳适配器
   * @returns {Promise<IBrowserAutomation>} 浏览器自动化实例
   */
  async createConfiguredBrowserAutomation(options = {}) {
    let adapterType = options.type;

    if (!adapterType && options.autoSelect !== false) {
      try {
        adapterType = await this.selectBestAdapter(options.preferredTypes);
      } catch (error) {
        adapterType = this.defaultAdapter;
      }
    }

    return this.createBrowserAutomation(adapterType || this.defaultAdapter);
  }
}

// 创建单例实例
const browserFactory = new BrowserFactory();

module.exports = {
  BrowserFactory,
  browserFactory
};
