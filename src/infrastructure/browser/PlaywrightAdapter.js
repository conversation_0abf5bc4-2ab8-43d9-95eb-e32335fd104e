let playwright;
try {
  playwright = require('playwright');
} catch (error) {
  // Playwright not installed, will be handled gracefully
  playwright = null;
}
const {
  IBrowserAutomation,
  IBrowser,
  IPage,
  IElementHandle,
  IResponse,
  BrowserLaunchOptions,
  NavigationOptions,
  WaitOptions,
  ScreenshotOptions
} = require('./IBrowserAutomation');

/**
 * Playwright 适配器实现
 */
class PlaywrightAdapter extends IBrowserAutomation {
  constructor(browserType = 'chromium') {
    super();
    this.browserType = browserType;
    this.playwright = this.getBrowserEngine(browserType);
  }

  getBrowserEngine(type) {
    if (!playwright) {
      throw new Error('Playwright is not installed. Please install it with: npm install playwright');
    }

    switch (type) {
      case 'firefox':
        return playwright.firefox;
      case 'webkit':
        return playwright.webkit;
      case 'chromium':
      default:
        return playwright.chromium;
    }
  }

  async launch(options = {}) {
    const launchOptions = new BrowserLaunchOptions(options);
    
    const playwrightOptions = {
      headless: launchOptions.headless,
      args: launchOptions.args,
      executablePath: launchOptions.executablePath,
      devtools: launchOptions.devtools,
      slowMo: launchOptions.slowMo
    };

    // 移除 null 值
    Object.keys(playwrightOptions).forEach(key => {
      if (playwrightOptions[key] === null) {
        delete playwrightOptions[key];
      }
    });

    const browser = await this.playwright.launch(playwrightOptions);
    return new PlaywrightBrowserAdapter(browser);
  }

  async detectBrowsers() {
    try {
      if (!playwright) {
        return [];
      }

      const browsers = [];

      // 检测 Chromium
      try {
        const browser = await playwright.chromium.launch({ headless: true });
        await browser.close();
        browsers.push({
          name: 'Chromium (Playwright)',
          type: 'playwright-chromium',
          executablePath: playwright.chromium.executablePath(),
          version: 'latest',
          available: true
        });
      } catch (e) {
        // Chromium 不可用
      }

      // 检测 Firefox
      try {
        const browser = await playwright.firefox.launch({ headless: true });
        await browser.close();
        browsers.push({
          name: 'Firefox (Playwright)',
          type: 'playwright-firefox',
          executablePath: playwright.firefox.executablePath(),
          version: 'latest',
          available: true
        });
      } catch (e) {
        // Firefox 不可用
      }

      // 检测 WebKit
      try {
        const browser = await playwright.webkit.launch({ headless: true });
        await browser.close();
        browsers.push({
          name: 'WebKit (Playwright)',
          type: 'playwright-webkit',
          executablePath: playwright.webkit.executablePath(),
          version: 'latest',
          available: true
        });
      } catch (e) {
        // WebKit 不可用
      }

      return browsers;
    } catch (error) {
      return [];
    }
  }

  async downloadBrowser() {
    try {
      // Playwright 浏览器通常在安装时自动下载
      // 这里可以添加手动安装逻辑
      return true;
    } catch (error) {
      return false;
    }
  }

  getBrowserType() {
    return 'playwright';
  }
}

/**
 * Playwright 浏览器适配器
 */
class PlaywrightBrowserAdapter extends IBrowser {
  constructor(browser) {
    super();
    this.browser = browser;
  }

  async newPage() {
    const page = await this.browser.newPage();
    return new PlaywrightPageAdapter(page);
  }

  async pages() {
    const contexts = this.browser.contexts();
    const allPages = [];
    for (const context of contexts) {
      const pages = context.pages();
      allPages.push(...pages.map(page => new PlaywrightPageAdapter(page)));
    }
    return allPages;
  }

  async close() {
    return await this.browser.close();
  }

  async version() {
    return this.browser.version();
  }
}

/**
 * Playwright 页面适配器
 */
class PlaywrightPageAdapter extends IPage {
  constructor(page) {
    super();
    this.page = page;
  }

  async goto(url, options = {}) {
    const navOptions = new NavigationOptions(options);
    
    // 转换 waitUntil 选项
    let waitUntil = navOptions.waitUntil;
    if (waitUntil === 'networkidle0') {
      waitUntil = 'networkidle';
    } else if (waitUntil === 'domcontentloaded') {
      waitUntil = 'domcontentloaded';
    }

    const response = await this.page.goto(url, {
      waitUntil: waitUntil,
      timeout: navOptions.timeout
    });
    return response ? new PlaywrightResponseAdapter(response) : null;
  }

  url() {
    return this.page.url();
  }

  async setViewport(viewport) {
    return await this.page.setViewportSize(viewport);
  }

  async screenshot(options = {}) {
    const screenshotOptions = new ScreenshotOptions(options);
    return await this.page.screenshot({
      path: screenshotOptions.path,
      fullPage: screenshotOptions.fullPage,
      type: screenshotOptions.type,
      quality: screenshotOptions.quality
    });
  }

  async evaluate(pageFunction, ...args) {
    return await this.page.evaluate(pageFunction, ...args);
  }

  async waitForSelector(selector, options = {}) {
    const waitOptions = new WaitOptions(options);
    
    const playwrightOptions = {
      timeout: waitOptions.timeout
    };
    
    if (waitOptions.visible) {
      playwrightOptions.state = 'visible';
    }

    const element = await this.page.waitForSelector(selector, playwrightOptions);
    return element ? new PlaywrightElementAdapter(element) : null;
  }

  async waitForFunction(pageFunction, options = {}, ...args) {
    return await this.page.waitForFunction(pageFunction, ...args, options);
  }

  async $(selector) {
    const element = await this.page.$(selector);
    return element ? new PlaywrightElementAdapter(element) : null;
  }

  async $$(selector) {
    const elements = await this.page.$$(selector);
    return elements.map(element => new PlaywrightElementAdapter(element));
  }

  setDefaultTimeout(timeout) {
    this.page.setDefaultTimeout(timeout);
  }

  setDefaultNavigationTimeout(timeout) {
    this.page.setDefaultNavigationTimeout(timeout);
  }

  on(event, handler) {
    // 转换事件名称
    let playwrightEvent = event;
    if (event === 'pageerror') {
      playwrightEvent = 'pageerror';
    } else if (event === 'requestfailed') {
      playwrightEvent = 'requestfailed';
    }
    
    this.page.on(playwrightEvent, handler);
  }

  off(event, handler) {
    let playwrightEvent = event;
    if (event === 'pageerror') {
      playwrightEvent = 'pageerror';
    } else if (event === 'requestfailed') {
      playwrightEvent = 'requestfailed';
    }
    
    this.page.off(playwrightEvent, handler);
  }

  async close() {
    return await this.page.close();
  }
}

/**
 * Playwright 元素适配器
 */
class PlaywrightElementAdapter extends IElementHandle {
  constructor(element) {
    super();
    this.element = element;
  }

  async click(options = {}) {
    return await this.element.click(options);
  }

  async type(text, options = {}) {
    // Playwright 使用 fill 或 type
    if (options.delay) {
      return await this.element.type(text, options);
    } else {
      return await this.element.fill(text);
    }
  }

  async textContent() {
    return await this.element.textContent();
  }

  async getAttribute(name) {
    return await this.element.getAttribute(name);
  }
}

/**
 * Playwright 响应适配器
 */
class PlaywrightResponseAdapter extends IResponse {
  constructor(response) {
    super();
    this.response = response;
  }

  status() {
    return this.response.status();
  }

  statusText() {
    return this.response.statusText();
  }

  ok() {
    return this.response.ok();
  }

  url() {
    return this.response.url();
  }
}

module.exports = {
  PlaywrightAdapter,
  PlaywrightBrowserAdapter,
  PlaywrightPageAdapter,
  PlaywrightElementAdapter,
  PlaywrightResponseAdapter
};
