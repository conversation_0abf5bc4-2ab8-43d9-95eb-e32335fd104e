/**
 * Vue Config 转换器
 * 专门处理 vue.config.js 中的 webpack 配置
 * 
 * 支持处理以下配置：
 * - configureWebpack 对象
 * - configureWebpack 函数
 * - chainWebpack 函数
 */

function transform(file, api, options) {
  const j = api.jscodeshift;
  const root = j(file.source);
  let hasModifications = false;

  // 处理 configureWebpack 对象配置
  root
    .find(j.ObjectProperty, {
      key: { name: 'configureWebpack' },
      value: { type: 'ObjectExpression' }
    })
    .forEach(path => {
      const configObj = path.value.value;
      
      // 处理 library target
      configObj.properties.forEach(prop => {
        if (prop.key.name === 'output' && prop.value.type === 'ObjectExpression') {
          prop.value.properties.forEach(outputProp => {
            if (outputProp.key.name === 'libraryTarget') {
              const libraryTargetValue = outputProp.value.value;
              
              // 创建新的 library 对象
              const libraryObj = j.objectExpression([
                j.objectProperty(
                  j.identifier('type'),
                  j.stringLiteral(libraryTargetValue)
                )
              ]);
              
              // 替换 libraryTarget 为 library 对象
              const libraryProp = j.objectProperty(
                j.identifier('library'),
                libraryObj
              );
              
              const outputProps = prop.value.properties.filter(p => 
                !(p.key.name === 'libraryTarget')
              );
              outputProps.push(libraryProp);
              prop.value.properties = outputProps;
              
              hasModifications = true;
            }
          });
        }
      });
      
      // 处理 target 设置
      configObj.properties.forEach(prop => {
        if (prop.key.name === 'target' && 
            (prop.value.value === 'web' || prop.value.value === 'node')) {
          // 替换为 false
          prop.value = j.booleanLiteral(false);
          hasModifications = true;
        }
      });
    });
  
  // 处理 configureWebpack 函数配置
  root
    .find(j.ObjectProperty, {
      key: { name: 'configureWebpack' },
      value: { type: 'FunctionExpression' }
    })
    .forEach(path => {
      // 查找函数体内的 output.libraryTarget 赋值
      j(path.value.value)
        .find(j.AssignmentExpression)
        .forEach(assignPath => {
          if (
            assignPath.value.left.type === 'MemberExpression' &&
            assignPath.value.left.property.name === 'libraryTarget' &&
            assignPath.value.left.object.type === 'MemberExpression' &&
            assignPath.value.left.object.property.name === 'output'
          ) {
            // 获取 libraryTarget 的值
            const libraryTargetValue = assignPath.value.right.value;
            
            // 创建 library 对象赋值
            const libraryAssign = j.assignmentExpression(
              '=',
              j.memberExpression(
                assignPath.value.left.object,
                j.identifier('library')
              ),
              j.objectExpression([
                j.objectProperty(
                  j.identifier('type'),
                  j.stringLiteral(libraryTargetValue)
                )
              ])
            );
            
            // 替换赋值语句
            j(assignPath).replaceWith(libraryAssign);
            hasModifications = true;
          }
        });
      
      // 查找函数体内的 target 赋值
      j(path.value.value)
        .find(j.AssignmentExpression)
        .forEach(assignPath => {
          if (
            assignPath.value.left.type === 'MemberExpression' &&
            assignPath.value.left.property.name === 'target' &&
            assignPath.value.right.type === 'StringLiteral' &&
            (assignPath.value.right.value === 'web' || assignPath.value.right.value === 'node')
          ) {
            // 替换为 false
            assignPath.value.right = j.booleanLiteral(false);
            hasModifications = true;
          }
        });
    });
  
  // 处理 chainWebpack 函数
  root
    .find(j.ObjectProperty, {
      key: { name: 'chainWebpack' }
    })
    .forEach(path => {
      // 查找 config.output.libraryTarget 调用
      j(path.value.value)
        .find(j.CallExpression, {
          callee: {
            type: 'MemberExpression',
            property: { name: 'libraryTarget' },
            object: {
              type: 'MemberExpression',
              property: { name: 'output' }
            }
          }
        })
        .forEach(callPath => {
          // 获取参数
          const args = callPath.value.arguments;
          if (args.length > 0 && args[0].type === 'StringLiteral') {
            const libraryTargetValue = args[0].value;
            
            // 创建 library 调用
            const libraryCall = j.callExpression(
              j.memberExpression(
                j.memberExpression(
                  callPath.value.callee.object.object,
                  j.identifier('output')
                ),
                j.identifier('library')
              ),
              [
                j.objectExpression([
                  j.objectProperty(
                    j.identifier('type'),
                    j.stringLiteral(libraryTargetValue)
                  )
                ])
              ]
            );
            
            // 替换调用
            j(callPath).replaceWith(libraryCall);
            hasModifications = true;
          }
        });
      
      // 查找 config.target 调用
      j(path.value.value)
        .find(j.CallExpression, {
          callee: {
            type: 'MemberExpression',
            property: { name: 'target' }
          }
        })
        .forEach(callPath => {
          // 获取参数
          const args = callPath.value.arguments;
          if (
            args.length > 0 && 
            args[0].type === 'StringLiteral' &&
            (args[0].value === 'web' || args[0].value === 'node')
          ) {
            // 替换为 false
            args[0] = j.booleanLiteral(false);
            hasModifications = true;
          }
        });
    });

  // 处理 JSON 导入
  root
    .find(j.ImportDeclaration)
    .filter(path => {
      const source = path.value.source.value;
      return source.endsWith('.json');
    })
    .forEach(path => {
      // 检查是否有命名导入
      const hasNamedImports = path.value.specifiers.some(
        specifier => specifier.type === 'ImportSpecifier'
      );
      
      if (hasNamedImports) {
        // 替换为默认导入
        const source = path.value.source;
        const defaultImport = j.importDefaultSpecifier(
          j.identifier(path.value.specifiers[0].local.name)
        );
        
        const newImport = j.importDeclaration(
          [defaultImport],
          source
        );
        
        j(path).replaceWith(newImport);
        hasModifications = true;
      }
    });

  return hasModifications ? root.toSource() : file.source;
}

module.exports = transform;
