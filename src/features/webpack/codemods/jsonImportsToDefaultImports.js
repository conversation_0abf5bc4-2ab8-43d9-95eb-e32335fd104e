/**
 * Webpack v5 Codemod: json-imports-to-default-imports
 * 
 * 将 JSON 文件的命名导入转换为默认导入
 * 
 * 转换示例：
 * // webpack 4
 * import { version } from './package.json';
 * console.log(version);
 * 
 * // webpack 5
 * import pkg from './package.json';
 * console.log(pkg.version);
 */

function transform(file, api, options) {
  const j = api.jscodeshift;
  const root = j(file.source);
  let dirtyFlag = false;

  // 确定原始引号样式
  const originalQuoteStyle = file.source.includes("'") ? 'single' : 'double';

  // 查找所有 import 声明
  root.find(j.ImportDeclaration).forEach((path) => {
    const importPath = path.node.source.value;

    // 检查是否是从 JSON 文件导入
    if (importPath.endsWith('.json')) {
      const specifiers = path.node.specifiers;

      // 检查是否有命名导入
      if (specifiers.some((specifier) => j.ImportSpecifier.check(specifier))) {
        // 确定默认导入标识符
        const importBaseName = importPath.split('/').pop().replace('.json', '');
        const defaultImportName = importBaseName === 'package' ? 'pkg' : importBaseName;
        const defaultImportIdentifier = j.identifier(defaultImportName);

        // 创建新的默认导入声明
        const newImportDeclaration = j.importDeclaration(
          [j.importDefaultSpecifier(defaultImportIdentifier)],
          j.literal(importPath)
        );

        // 替换旧的导入声明
        j(path).replaceWith(newImportDeclaration);

        // 替换所有对命名导入的引用为默认导入的属性
        specifiers.forEach((specifier) => {
          if (j.ImportSpecifier.check(specifier)) {
            const localName = specifier.local.name;
            const importedName = specifier.imported.name;

            // 查找并替换所有使用该标识符的地方
            root
              .find(j.Identifier, { name: localName })
              .filter((idPath) => {
                // 排除在导入声明中使用的标识符
                const parent = idPath.parent.node;
                return (
                  !j.ImportDeclaration.check(parent) &&
                  !j.ImportSpecifier.check(parent) &&
                  !j.ImportDefaultSpecifier.check(parent) &&
                  !j.ImportNamespaceSpecifier.check(parent)
                );
              })
              .forEach((identifierPath) => {
                const parent = identifierPath.parent.node;
                
                // 检查是否已经是成员表达式的一部分
                if (
                  j.MemberExpression.check(parent) &&
                  parent.object === identifierPath.node
                ) {
                  // 如果已经是成员表达式的对象部分，替换对象
                  j(identifierPath).replaceWith(
                    j.memberExpression(
                      defaultImportIdentifier,
                      j.identifier(importedName)
                    )
                  );
                } else {
                  // 否则，创建新的成员表达式
                  j(identifierPath).replaceWith(
                    j.memberExpression(
                      defaultImportIdentifier,
                      j.identifier(importedName)
                    )
                  );
                }
              });
          }
        });

        dirtyFlag = true;
      }
    }
  });

  // 处理 require 语句的情况
  root.find(j.VariableDeclarator).forEach((path) => {
    const init = path.node.init;

    // 检查是否是 require JSON 文件的解构赋值
    if (
      j.CallExpression.check(init) &&
      j.Identifier.check(init.callee) &&
      init.callee.name === 'require' &&
      init.arguments.length === 1 &&
      j.Literal.check(init.arguments[0]) &&
      init.arguments[0].value.endsWith('.json') &&
      j.ObjectPattern.check(path.node.id)
    ) {
      const jsonPath = init.arguments[0].value;
      const destructuredProps = path.node.id.properties;

      // 创建默认导入名称
      const importBaseName = jsonPath.split('/').pop().replace('.json', '');
      const defaultVarName = importBaseName === 'package' ? 'pkg' : importBaseName;

      // 替换当前声明为普通的 require
      path.node.id = j.identifier(defaultVarName);

      // 创建新的变量声明用于解构的属性
      const newDeclarations = destructuredProps.map(prop => {
        if (j.Property.check(prop) && j.Identifier.check(prop.key)) {
          const propName = prop.key.name;
          const localName = j.Identifier.check(prop.value) ? prop.value.name : propName;

          return j.variableDeclarator(
            j.identifier(localName),
            j.memberExpression(j.identifier(defaultVarName), j.identifier(propName))
          );
        }
        return null;
      }).filter(Boolean);

      // 在当前声明后插入新的声明
      if (newDeclarations.length > 0) {
        const parentPath = path.parent;
        if (j.VariableDeclaration.check(parentPath.node)) {
          const newVariableDeclaration = j.variableDeclaration(
            parentPath.node.kind,
            newDeclarations
          );

          // 在当前声明后插入
          j(parentPath).insertAfter(newVariableDeclaration);
        }
      }

      dirtyFlag = true;
    }
  });

  return dirtyFlag ? root.toSource({ 
    quote: originalQuoteStyle,
    reuseParsers: true
  }) : undefined;
}

module.exports = transform;
