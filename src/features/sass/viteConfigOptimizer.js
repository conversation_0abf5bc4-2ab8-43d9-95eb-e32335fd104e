const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * Vite 配置优化器
 * 自动检测和优化 Vite 配置以支持 Sass 模块系统
 */
class ViteConfigOptimizer {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      configFile: 'vite.config.js',
      backup: true,
      verbose: false,
      ...options
    };
    
    this.configPath = path.join(this.projectPath, this.options.configFile);
    this.currentConfig = null;
    this.optimizations = [];
  }

  /**
   * 执行 Vite 配置优化
   */
  async optimize() {
    console.log(chalk.blue('⚙️  开始 Vite 配置优化...'));
    
    try {
      // 1. 检查配置文件是否存在
      if (!(await this.configExists())) {
        await this.createDefaultConfig();
      }
      
      // 2. 分析当前配置
      await this.analyzeCurrentConfig();
      
      // 3. 生成优化建议
      await this.generateOptimizations();
      
      // 4. 应用优化
      await this.applyOptimizations();
      
      // 5. 验证配置
      const validation = await this.validateConfig();
      
      console.log(chalk.green('✅ Vite 配置优化完成'));
      
      return {
        optimizations: this.optimizations,
        validation: validation
      };
      
    } catch (error) {
      console.error(chalk.red(`Vite 配置优化失败: ${error.message}`));
      throw error;
    }
  }

  /**
   * 检查配置文件是否存在
   */
  async configExists() {
    return await fs.pathExists(this.configPath);
  }

  /**
   * 创建默认配置
   */
  async createDefaultConfig() {
    console.log(chalk.gray('创建默认 Vite 配置文件...'));
    
    const defaultConfig = `import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        loadPaths: [path.resolve(__dirname, 'node_modules')],
        // 可选：全局变量注入（谨慎使用）
        // additionalData: \`@use 'src/styles' as *;\`
      }
    }
  }
});`;

    await fs.writeFile(this.configPath, defaultConfig, 'utf8');
    
    console.log(chalk.green(`✅ 创建默认配置: ${this.options.configFile}`));
  }

  /**
   * 分析当前配置
   */
  async analyzeCurrentConfig() {
    if (!(await this.configExists())) {
      return;
    }
    
    const configContent = await fs.readFile(this.configPath, 'utf8');
    this.currentConfig = this.parseConfig(configContent);
    
    if (this.options.verbose) {
      console.log(chalk.gray('分析当前 Vite 配置...'));
    }
  }

  /**
   * 解析配置文件内容
   */
  parseConfig(content) {
    const config = {
      hasVuePlugin: false,
      hasPathAlias: false,
      hasSassConfig: false,
      hasLoadPaths: false,
      hasAdditionalData: false,
      content: content
    };
    
    // 检查 Vue 插件
    config.hasVuePlugin = /vue\(\)/.test(content);
    
    // 检查路径别名
    config.hasPathAlias = /alias\s*:\s*\{/.test(content);
    
    // 检查 Sass 配置
    config.hasSassConfig = /scss\s*:\s*\{/.test(content);
    
    // 检查 loadPaths
    config.hasLoadPaths = /loadPaths\s*:/.test(content);
    
    // 检查 additionalData
    config.hasAdditionalData = /additionalData\s*:/.test(content);
    
    return config;
  }

  /**
   * 生成优化建议
   */
  async generateOptimizations() {
    if (!this.currentConfig) {
      return;
    }
    
    // 检查 Sass loadPaths 配置
    if (!this.currentConfig.hasLoadPaths) {
      this.optimizations.push({
        id: 'add-load-paths',
        title: '添加 Sass loadPaths 配置',
        description: '配置 loadPaths 以支持 node_modules 路径解析',
        priority: 'high',
        required: true
      });
    }
    
    // 检查路径别名
    if (!this.currentConfig.hasPathAlias) {
      this.optimizations.push({
        id: 'add-path-alias',
        title: '添加路径别名配置',
        description: '配置 @ 别名指向 src 目录',
        priority: 'medium',
        required: false
      });
    }
    
    // 检查 Vue 插件
    if (!this.currentConfig.hasVuePlugin) {
      this.optimizations.push({
        id: 'add-vue-plugin',
        title: '添加 Vue 插件',
        description: '确保 Vue 单文件组件正确处理',
        priority: 'high',
        required: true
      });
    }
    
    // 检查是否使用了 additionalData（可能的反模式）
    if (this.currentConfig.hasAdditionalData) {
      this.optimizations.push({
        id: 'review-additional-data',
        title: '检查 additionalData 使用',
        description: 'additionalData 可能导致性能问题，建议谨慎使用',
        priority: 'low',
        required: false
      });
    }
    
    // 检查是否需要 Element Plus 配置
    if (await this.needsElementPlusConfig()) {
      this.optimizations.push({
        id: 'add-element-plus-config',
        title: '添加 Element Plus 配置',
        description: '优化 Element Plus 的样式处理',
        priority: 'medium',
        required: false
      });
    }
  }

  /**
   * 检查是否需要 Element Plus 配置
   */
  async needsElementPlusConfig() {
    try {
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        return !!allDeps['element-plus'];
      }
    } catch (error) {
      // 忽略错误
    }
    return false;
  }

  /**
   * 应用优化
   */
  async applyOptimizations() {
    if (this.optimizations.length === 0) {
      console.log(chalk.green('配置已是最优状态，无需优化'));
      return;
    }
    
    // 备份原配置
    if (this.options.backup) {
      await this.backupConfig();
    }
    
    let configContent = this.currentConfig.content;
    
    for (const optimization of this.optimizations) {
      if (optimization.required || this.options.applyAll) {
        configContent = await this.applyOptimization(configContent, optimization);
        
        if (this.options.verbose) {
          console.log(chalk.gray(`应用优化: ${optimization.title}`));
        }
      }
    }
    
    await fs.writeFile(this.configPath, configContent, 'utf8');
    
    const appliedCount = this.optimizations.filter(o => o.required || this.options.applyAll).length;
    console.log(chalk.green(`✅ 应用了 ${appliedCount} 项优化`));
  }

  /**
   * 备份配置文件
   */
  async backupConfig() {
    const backupPath = `${this.configPath}.backup`;
    await fs.copy(this.configPath, backupPath);
    
    if (this.options.verbose) {
      console.log(chalk.gray(`备份配置文件: ${path.basename(backupPath)}`));
    }
  }

  /**
   * 应用单个优化
   */
  async applyOptimization(configContent, optimization) {
    switch (optimization.id) {
      case 'add-load-paths':
        return this.addLoadPaths(configContent);
      
      case 'add-path-alias':
        return this.addPathAlias(configContent);
      
      case 'add-vue-plugin':
        return this.addVuePlugin(configContent);
      
      case 'add-element-plus-config':
        return this.addElementPlusConfig(configContent);
      
      default:
        return configContent;
    }
  }

  /**
   * 添加 loadPaths 配置
   */
  addLoadPaths(content) {
    // 检查是否已有 css 配置
    if (content.includes('css:')) {
      // 检查是否已有 preprocessorOptions
      if (content.includes('preprocessorOptions:')) {
        // 检查是否已有 scss 配置
        if (content.includes('scss:')) {
          // 在 scss 配置中添加 loadPaths
          const scssRegex = /(scss\s*:\s*\{)/;
          const replacement = '$1\n        loadPaths: [path.resolve(__dirname, \'node_modules\')],';
          return content.replace(scssRegex, replacement);
        } else {
          // 添加 scss 配置
          const preprocessorRegex = /(preprocessorOptions\s*:\s*\{)/;
          const replacement = '$1\n      scss: {\n        loadPaths: [path.resolve(__dirname, \'node_modules\')]\n      },';
          return content.replace(preprocessorRegex, replacement);
        }
      } else {
        // 添加 preprocessorOptions
        const cssRegex = /(css\s*:\s*\{)/;
        const replacement = '$1\n    preprocessorOptions: {\n      scss: {\n        loadPaths: [path.resolve(__dirname, \'node_modules\')]\n      }\n    },';
        return content.replace(cssRegex, replacement);
      }
    } else {
      // 添加完整的 css 配置
      const exportRegex = /(export default (?:defineConfig\()?{)/;
      const replacement = '$1\n  css: {\n    preprocessorOptions: {\n      scss: {\n        loadPaths: [path.resolve(__dirname, \'node_modules\')]\n      }\n    }\n  },';
      return content.replace(exportRegex, replacement);
    }
  }

  /**
   * 添加路径别名配置
   */
  addPathAlias(content) {
    if (content.includes('resolve:')) {
      if (content.includes('alias:')) {
        // 在现有 alias 中添加 @ 别名
        const aliasRegex = /(alias\s*:\s*\{)/;
        const replacement = '$1\n      \'@\': path.resolve(__dirname, \'src\'),';
        return content.replace(aliasRegex, replacement);
      } else {
        // 添加 alias 配置
        const resolveRegex = /(resolve\s*:\s*\{)/;
        const replacement = '$1\n    alias: {\n      \'@\': path.resolve(__dirname, \'src\')\n    },';
        return content.replace(resolveRegex, replacement);
      }
    } else {
      // 添加完整的 resolve 配置
      const exportRegex = /(export default (?:defineConfig\()?{)/;
      const replacement = '$1\n  resolve: {\n    alias: {\n      \'@\': path.resolve(__dirname, \'src\')\n    }\n  },';
      return content.replace(exportRegex, replacement);
    }
  }

  /**
   * 添加 Vue 插件
   */
  addVuePlugin(content) {
    // 检查是否已导入 vue 插件
    if (!content.includes('from \'@vitejs/plugin-vue\'') && !content.includes('import vue')) {
      // 在第一个import语句后添加vue导入
      if (content.includes('import')) {
        content = content.replace(
          /(import.*?['"];?\n)/,
          '$1import vue from \'@vitejs/plugin-vue\';\n'
        );
      } else {
        // 如果没有import语句，在文件开头添加
        content = 'import vue from \'@vitejs/plugin-vue\';\n' + content;
      }
    }
    
    // 添加到 plugins 数组
    if (content.includes('plugins:')) {
      // 检查是否已经有vue()插件
      if (!content.includes('vue()')) {
        const pluginsRegex = /(plugins\s*:\s*\[)/;
        const replacement = '$1vue(), ';
        return content.replace(pluginsRegex, replacement);
      }
      return content;
    } else {
      const exportRegex = /(export default (?:defineConfig\()?{)/;
      const replacement = '$1\n  plugins: [vue()],';
      return content.replace(exportRegex, replacement);
    }
  }

  /**
   * 添加 Element Plus 配置
   */
  addElementPlusConfig(content) {
    // 这里可以添加 Element Plus 特定的优化配置
    // 例如自动导入、样式优化等
    
    const comment = '\n  // Element Plus 优化配置\n  // 可以在这里添加自动导入等配置\n';
    
    const exportRegex = /(export default (?:defineConfig\()?{)/;
    return content.replace(exportRegex, '$1' + comment);
  }

  /**
   * 验证配置
   */
  async validateConfig() {
    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };
    
    try {
      // 重新分析配置
      await this.analyzeCurrentConfig();
      
      // 检查必需的配置
      if (!this.currentConfig.hasLoadPaths) {
        validation.errors.push('缺少 Sass loadPaths 配置');
        validation.isValid = false;
      }
      
      if (!this.currentConfig.hasVuePlugin) {
        validation.warnings.push('建议添加 Vue 插件');
      }
      
      if (!this.currentConfig.hasPathAlias) {
        validation.suggestions.push('建议添加路径别名配置');
      }
      
      // 检查语法错误（简单检查）
      const configContent = await fs.readFile(this.configPath, 'utf8');
      if (!this.isValidJavaScript(configContent)) {
        validation.errors.push('配置文件语法错误');
        validation.isValid = false;
      }
      
    } catch (error) {
      validation.errors.push(`验证失败: ${error.message}`);
      validation.isValid = false;
    }
    
    return validation;
  }

  /**
   * 简单的 JavaScript 语法检查
   */
  isValidJavaScript(content) {
    try {
      // 更复杂的语法检查：使用栈来检查括号匹配
      const stack = [];
      const pairs = { '(': ')', '[': ']', '{': '}' };
      let inString = false;
      let stringChar = '';
      
      for (let i = 0; i < content.length; i++) {
        const char = content[i];
        const prevChar = i > 0 ? content[i - 1] : '';
        
        // 处理字符串
        if ((char === '"' || char === "'") && prevChar !== '\\') {
          if (!inString) {
            inString = true;
            stringChar = char;
          } else if (char === stringChar) {
            inString = false;
            stringChar = '';
          }
          continue;
        }
        
        // 如果在字符串中，跳过括号检查
        if (inString) {
          continue;
        }
        
        if (char === '(' || char === '[' || char === '{') {
          stack.push(char);
        } else if (char === ')' || char === ']' || char === '}') {
          if (stack.length === 0) {
            return false; // 没有对应的开括号
          }
          
          const last = stack.pop();
          if (pairs[last] !== char) {
            return false; // 括号类型不匹配
          }
        }
      }
      
      // 如果栈不为空，说明有未闭合的括号
      if (stack.length > 0) {
        return false;
      }
      
      // 如果还在字符串中，说明字符串未闭合
      if (inString) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * 生成配置建议报告
   */
  generateConfigReport() {
    const report = {
      configFile: this.options.configFile,
      optimizations: this.optimizations,
      recommendations: []
    };
    
    // 添加最佳实践建议
    report.recommendations.push({
      title: 'Sass 模块系统最佳实践',
      items: [
        '使用 @use 和 @forward 替代 @import',
        '配置 loadPaths 支持 node_modules 路径解析',
        '避免使用 additionalData 进行全局变量注入',
        '使用桶文件模式组织样式架构'
      ]
    });
    
    report.recommendations.push({
      title: 'Vite 配置优化建议',
      items: [
        '配置路径别名提高开发体验',
        '启用 CSS 代码分割优化性能',
        '配置适当的构建优化选项',
        '使用环境变量管理不同环境配置'
      ]
    });
    
    return report;
  }

  /**
   * 打印优化结果
   */
  printOptimizationResults(result) {
    console.log('\n' + chalk.bold('📊 Vite 配置优化结果:'));
    
    if (result.optimizations.length > 0) {
      console.log('\n' + chalk.bold('应用的优化:'));
      result.optimizations.forEach((opt, index) => {
        const icon = opt.required ? '✅' : '💡';
        console.log(`${icon} ${opt.title}`);
        console.log(`   ${opt.description}`);
      });
    }
    
    if (result.validation) {
      console.log('\n' + chalk.bold('配置验证:'));
      
      if (result.validation.isValid) {
        console.log(chalk.green('✅ 配置验证通过'));
      } else {
        console.log(chalk.red('❌ 配置验证失败'));
        result.validation.errors.forEach(error => {
          console.log(chalk.red(`   错误: ${error}`));
        });
      }
      
      if (result.validation.warnings.length > 0) {
        result.validation.warnings.forEach(warning => {
          console.log(chalk.yellow(`   警告: ${warning}`));
        });
      }
      
      if (result.validation.suggestions.length > 0) {
        result.validation.suggestions.forEach(suggestion => {
          console.log(chalk.blue(`   建议: ${suggestion}`));
        });
      }
    }
  }
}

module.exports = ViteConfigOptimizer;
