const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

const { contextManager } = require('../core/context/ContextManager');
const ContextAwareComponent = require('../core/context/ContextAwareComponent');
const ProjectDetector = require('../utils/ProjectDetector');

// 导入各个迁移组件
const PackageJsonManager = require('../features/package-json/PackageJsonManager');
const VueCodeMigrator = require('../frameworks/vue/VueCodeTransformer');
const FailureLogger = require('../utils/FailureLogger');
const AIRepairer = require('../frameworks/vue/utils/VueRepairerService');
const ESLintFixer = require('../features/lint/ESLintFixer');
const BuildFixer = require('../domain/build-fix/BuildFixer');
const SassMigrator = require('./SassMigrator');

// 导入源到目标迁移相关组件
const DependencyMapper = require('../frameworks/vue/third-party/ComponentDependencyMapper');

// 导入 AI 构建修复代理
const BuildFixAgent = require('../domain/build-fix/ai/BuildFixAgent');

// 导入迁移文档生成器
const MigrationDocGenerator = require('../frameworks/vue/third-party/MigrationDocGenerator');

/**
 * 自动化迁移器
 * 根据项目类型自动应用最佳配置进行迁移
 * 支持原地迁移和源到目标迁移两种模式
 */
class AutoMigrator extends ContextAwareComponent {
  constructor(projectPath, options = {}) {
    super('AutoMigrator', options);

    this.projectPath = path.resolve(projectPath);
    this.options = options;

    // 源到目标迁移模式支持
    this.sourceToTargetMode = options.sourceToTargetMode || false;
    this.sourceProjectPath = options.sourceProjectPath ? path.resolve(options.sourceProjectPath) : null;
    this.targetProjectPath = options.targetProjectPath ? path.resolve(options.targetProjectPath) : null;

    // 新目录迁移模式支持（保持向后兼容）
    this.newDirectoryMode = options.newDirectoryMode || false;
    this.destinationPath = options.destinationPath;

    // 确定工作路径
    if (this.sourceToTargetMode) {
      this.workingPath = this.targetProjectPath;
    } else if (this.newDirectoryMode) {
      this.workingPath = this.destinationPath;
    } else {
      this.workingPath = this.projectPath;
    }

    this.detector = new ProjectDetector(this.projectPath);
    this.presetConfig = null;
    this.spinner = null;
    this.failedFiles = [];

    // 源到目标迁移相关组件
    if (this.sourceToTargetMode) {
      this.dependencyMapper = new DependencyMapper();
      this.dependencyCodeMigrator = null; // 将在后续初始化
    }

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      totalSteps: this.sourceToTargetMode ? 8 : 7, // 源到目标模式多一个步骤
      completedSteps: 0,
      success: false,
      errors: [],
      stepResults: {}
    };

    // 初始化迁移上下文
    this.migrationContext = null;
    this.contextId = null;

    // 输出控制
    this.quiet = options.quiet || false;
  }

  /**
   * 条件输出日志 - 仅在非静默模式下输出
   */
  log(message, ...args) {
    if (!this.quiet) {
      console.log(message, ...args);
    }
  }

  /**
   * 条件输出灰色日志 - 仅在非静默模式下输出
   */
  logGray(message, ...args) {
    if (!this.quiet) {
      console.log(chalk.gray(message), ...args);
    }
  }

  /**
   * 条件输出黄色警告 - 仅在非静默模式下输出
   */
  logYellow(message, ...args) {
    if (!this.quiet) {
      console.log(chalk.yellow(message), ...args);
    }
  }

  /**
   * 总是输出的日志（错误和关键信息）
   */
  logAlways(message, ...args) {
    console.log(message, ...args);
  }

  /**
   * 初始化迁移上下文
   */
  async initializeMigrationContext() {
    // 创建迁移上下文
    const { contextId, context } = contextManager.createContext(this.projectPath, {
      mode: this.sourceToTargetMode ? 'source-to-target' : 'in-place',
      sourcePath: this.sourceProjectPath,
      targetPath: this.targetProjectPath,
      workingPath: this.workingPath,
      options: this.options
    });

    this.contextId = contextId;
    this.migrationContext = context;

    context.setProjectInfo({
      path: this.projectPath,
      name: path.basename(this.projectPath),
      type: 'unknown' // 将在检测阶段更新
    });

    context.setConfig({
      sourceToTargetMode: this.sourceToTargetMode,
      sourcePath: this.sourceProjectPath,
      targetPath: this.targetProjectPath,
      workingPath: this.workingPath,
      newDirectoryMode: this.newDirectoryMode,
      destinationPath: this.destinationPath
    });

    await this.initialize(contextId);
    console.log(chalk.blue(`🎯 迁移上下文已创建: ${contextId}`));
  }

  /**
   * 执行自动化迁移
   */
  async migrate() {
    await this.initializeMigrationContext()

    if (this.sourceToTargetMode) {
      this.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具 - 源到目标模式\n'))
      this.log(chalk.gray(`源项目: ${this.sourceProjectPath}`))
      this.log(chalk.gray(`目标项目: ${this.targetProjectPath}`))
    } else if (this.newDirectoryMode) {
      this.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具 - 新目录模式\n'))
      this.log(chalk.gray(`源项目: ${this.projectPath}`))
      this.log(chalk.gray(`目标项目: ${this.destinationPath}`))
    } else {
      this.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具\n'))
      this.log(chalk.gray(`项目路径: ${this.projectPath}`))
    }
    this.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}\n`))

    // 1. 如果是源到目标模式，先复制源项目文件
    if (this.sourceToTargetMode) {
      this.migrationContext.startPhase('copy-source-to-target')
      await this.copySourceToTarget()
      this.migrationContext.completePhase('copy-source-to-target')
    } else if (this.newDirectoryMode) {
      this.migrationContext.startPhase('copy-to-destination')
      await this.copyProjectToDestination()
      this.migrationContext.completePhase('copy-to-destination')
    }

    this.migrationContext.startPhase('project-detection')
    await this.detectProject()
    this.migrationContext.completePhase('project-detection')

    // 4. 执行迁移（在工作目录中进行）
    this.migrationContext.startPhase('execute-migration')
    await this.executeMigration()
    this.migrationContext.completePhase('execute-migration')

    // 完成迁移
    this.migrationContext.completeMigration()

    // 保存上下文快照
    await this.migrationContext.saveToFile()

    this.logAlways(chalk.bold.green('\n🎉 自动化迁移完成！'))

    if (this.sourceToTargetMode) {
      this.logAlways(chalk.green(`✅ 迁移后的项目位于: ${this.targetProjectPath}`))
    } else if (this.newDirectoryMode) {
      this.logAlways(chalk.green(`✅ 迁移后的项目位于: ${this.destinationPath}`))
    }

    this.displayMigrationSummary()
  }

  /**
   * 复制源项目到目标项目（源到目标模式）
   */
  async copySourceToTarget() {
    this.spinner = ora('复制源项目文件到目标项目...').start();

    try {
      await fs.ensureDir(this.targetProjectPath);

      const sourceSrcPath = path.join(this.sourceProjectPath, 'src');
      if (await fs.pathExists(sourceSrcPath)) {
        const targetSrcPath = path.join(this.targetProjectPath, 'src');
        this.logGray(`   复制 src 目录: ${sourceSrcPath} → ${targetSrcPath}`);

        await fs.copy(sourceSrcPath, targetSrcPath, {
          overwrite: true,
          filter: (src) => {
            // 排除一些不必要的文件
            const fileName = path.basename(src);
            return !fileName.startsWith('.') &&
                   fileName !== 'node_modules' &&
                   !fileName.endsWith('.log');
          }
        });
      } else {
        throw new Error('源项目中未找到 src 目录');
      }

      await this.smartCopyAdditionalFiles();
      this.spinner.succeed('源项目文件复制完成');
      this.logGray(`  复制到: ${this.targetProjectPath}`);
    } catch (error) {
      this.spinner.fail('源项目文件复制失败');
      throw error;
    }
  }

  /**
   * 复制项目到目标目录（新目录模式，保持向后兼容）
   */
  async copyProjectToDestination() {
    this.spinner = ora('复制项目到目标目录...').start();

    try {
      await fs.ensureDir(path.dirname(this.destinationPath));
      if (await fs.pathExists(this.destinationPath)) {
        await fs.remove(this.destinationPath);
      }

      await fs.copy(this.projectPath, this.destinationPath, {
        filter: (src) => {
          const relativePath = path.relative(this.projectPath, src);

          // 排除的文件和目录
          const excludePatterns = [
            'node_modules',
            '.git',
            'dist',
            'build',
            '.DS_Store',
            '.env.local',
            '.env.*.local',
            'npm-debug.log*',
            'yarn-debug.log*',
            'yarn-error.log*',
            'coverage',
            '.nyc_output',
            '.cache',
            '.temp',
            '.tmp'
          ];

          // 检查是否应该排除
          for (const pattern of excludePatterns) {
            if (relativePath === pattern || relativePath.startsWith(pattern + '/')) {
              return false;
            }
          }

          return true;
        }
      });

      this.spinner.succeed(`项目已复制到: ${this.destinationPath}`);

      // 更新检测器指向新的工作目录
      this.detector = new ProjectDetector(this.destinationPath);

      // 智能复制其他必要文件（为新目录模式也添加此功能）
      const originalProjectPath = this.projectPath;
      const originalSourcePath = this.sourceToTargetMode ? this.sourceProjectPath : null;

      // 临时调整路径用于智能复制
      this.projectPath = originalProjectPath;
      this.sourceProjectPath = originalProjectPath;
      this.targetProjectPath = this.destinationPath;

      await this.smartCopyAdditionalFiles();

      // 恢复原始路径
      this.projectPath = this.destinationPath;
      this.sourceProjectPath = originalSourcePath;
      this.targetProjectPath = null;
    } catch (error) {
      this.spinner.fail('项目复制失败');
      throw new Error(`复制项目失败: ${error.message}`);
    }
  }

  /**
   * 智能复制其他必要文件
   * 使用 AI 分析项目结构，或使用默认策略复制常见必要文件
   */
  async smartCopyAdditionalFiles() {
    this.log(chalk.blue('\n🧠 智能分析项目结构，确定需要复制的文件...'));

    try {
      // 检查是否有 AI 能力
      const buildFixAgent = new BuildFixAgent(this.sourceProjectPath || this.projectPath, {
        aiApiKey: this.options?.aiApiKey || this.options.aiApiKey,
        verbose: this.options.verbose
      });

      if (buildFixAgent.isEnabled()) {
        this.logGray('   使用 AI 分析项目结构...');
        await this.aiBasedFileCopy(buildFixAgent);
      } else {
        this.logGray('   AI 不可用，使用默认策略复制文件...');
        await this.defaultFileCopy();
      }
    } catch (error) {
      this.logYellow(`⚠️  智能复制失败，使用默认策略: ${error.message}`);
      await this.defaultFileCopy();
    }
  }

  /**
   * 基于 AI 的文件复制
   */
  async aiBasedFileCopy(buildFixAgent) {
    try {
      const sourceDir = this.sourceProjectPath || this.projectPath;

      // 使用 AI 分析项目结构，确定需要复制的文件和目录
      const analysisPrompt = this.generateProjectAnalysisPrompt(sourceDir);
      const analysisResponse = await buildFixAgent.callAI(analysisPrompt);

      // 解析 AI 响应，获取需要复制的文件列表
      const filesToCopy = this.parseProjectAnalysisResponse(analysisResponse);

      if (filesToCopy.length > 0) {
        console.log(chalk.gray(`   AI 识别出 ${filesToCopy.length} 个需要复制的文件/目录:`));

        for (const item of filesToCopy) {
          await this.copyFileOrDirectory(item, sourceDir);
        }
      } else {
        console.log(chalk.gray('   AI 未识别出额外需要复制的文件，使用默认策略...'));
        await this.defaultFileCopy();
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  AI 分析失败: ${error.message}`));
      await this.defaultFileCopy();
    }
  }

  generateProjectAnalysisPrompt(sourceDir) {
    return `你是一个专业的 Vue 项目分析专家。请分析以下 Vue 项目的结构，确定在迁移过程中除了 src 目录和 package.json 之外，还需要复制哪些文件和目录。

**任务目标**：
1. 分析项目结构，识别运行项目所必需的文件和目录
2. 重点关注可能影响项目运行的配置文件、资源文件、工具文件等
3. 排除不必要的文件（如缓存、日志、临时文件等）

**项目路径**：${sourceDir}

**你可以使用以下工具来分析项目结构**：
- list_files: 列出目录中的文件
- read_file: 读取文件内容进行分析

**常见需要复制的文件/目录类型**：
- mock 数据目录（如 mock/、tests/fixtures/）
- 静态资源目录（如 public/、static/、assets/）
- 配置文件（vue.config.js、vite.config.js、babel.config.js、.eslintrc.js、tsconfig.json）
- 环境配置文件（.env.*）
- 脚本文件目录（如 scripts/、build/）
- 文档目录（如 docs/，如果包含运行相关的文档）
- 其他工具配置文件（.gitignore、.editorconfig、jest.config.js 等）

**分析步骤**：
1. 首先使用 list_files 工具查看项目根目录
2. 识别重要的配置文件和目录
3. 对于可能重要的目录，进一步查看其内容
4. 判断每个文件/目录对项目运行的重要性

**响应格式**：
请使用以下 XML 格式返回分析结果：

\`\`\`xml
<project_analysis>
<files_to_copy>
<item type="directory">mock</item>
<item type="file">.env.development</item>
<item type="directory">public</item>
</files_to_copy>
<reasoning>
说明为什么选择这些文件/目录进行复制，以及它们对项目运行的重要性
</reasoning>
</project_analysis>
\`\`\`

**重要约束**：
- 不要包含已经复制的 src 目录和 package.json, vue.config.js
- 不要包含 node_modules、dist、build、.git 等目录
- 不要包含日志文件、缓存文件、临时文件
- 重点关注对项目运行有实际影响的文件

请开始分析项目结构。`;
  }

  /**
   * 解析项目分析响应
   */
  parseProjectAnalysisResponse(response) {
    try {
      const filesToCopy = [];

      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<project_analysis>[\s\S]*?<files_to_copy>([\s\S]*?)<\/files_to_copy>[\s\S]*?<\/project_analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const itemMatches = filesSection.match(/<item\s+type="(file|directory)">(.*?)<\/item>/g);

        if (itemMatches) {
          itemMatches.forEach(match => {
            const typeMatch = match.match(/type="(file|directory)"/);
            const pathMatch = match.match(/<item[^>]*>(.*?)<\/item>/);

            if (typeMatch && pathMatch) {
              const type = typeMatch[1];
              const filePath = pathMatch[1].trim();

              if (filePath && this.isValidFileForCopy(filePath)) {
                filesToCopy.push({ type, path: filePath });
              }
            }
          });
        }
      }

      return filesToCopy;
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 项目分析响应失败'));
      return [];
    }
  }

  /**
   * 验证文件是否适合复制
   */
  isValidFileForCopy(filePath) {
    // 排除已经处理的文件和目录
    const excludeList = [
      'src', 'node_modules', 'dist', 'build', '.git',
      'package.json', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml',
      '.DS_Store', 'Thumbs.db'
    ];

    // 排除日志文件
    const logPattern = /\.log$/i;
    if (logPattern.test(filePath)) {
      return false;
    }

    return !excludeList.some(excluded => {
      return filePath === excluded || filePath.startsWith(excluded + '/');
    });
  }

  /**
   * 复制文件或目录
   */
  async copyFileOrDirectory(item, sourceDir) {
    const sourceItemPath = path.join(sourceDir, item.path);
    const targetItemPath = path.join(this.targetProjectPath || this.destinationPath, item.path);

    try {
      if (!(await fs.pathExists(sourceItemPath))) {
        this.logYellow(`   ⚠️  源文件不存在，跳过: ${item.path}`);
        return;
      }

      const stat = await fs.stat(sourceItemPath);

      if (item.type === 'directory' && stat.isDirectory()) {
        this.logGray(`   复制目录: ${item.path}`);
        await fs.copy(sourceItemPath, targetItemPath, {
          overwrite: true,
          filter: (src) => {
            const fileName = path.basename(src);
            return !fileName.startsWith('.') &&
                   fileName !== 'node_modules' &&
                   !fileName.endsWith('.log');
          }
        });
      } else if (item.type === 'file' && stat.isFile()) {
        this.logGray(`   复制文件: ${item.path}`);
        await fs.ensureDir(path.dirname(targetItemPath));
        await fs.copy(sourceItemPath, targetItemPath, { overwrite: true });
      } else {
        this.logYellow(`   ⚠️  类型不匹配，跳过: ${item.path} (期望: ${item.type})`);
      }
    } catch (error) {
      this.logYellow(`   ⚠️  复制失败: ${item.path} - ${error.message}`);
    }
  }

  /**
   * 默认文件复制策略
   */
  async defaultFileCopy() {
    const sourceDir = this.sourceProjectPath || this.projectPath;
    const targetDir = this.targetProjectPath || this.destinationPath;

    // 默认需要复制的配置文件
    const defaultConfigFiles = [
      // 'vue.config.js',
      'vite.config.js',
      'vite.config.ts',
      'babel.config.js',
      // '.eslintrc.js',
      // '.eslintrc.json',
      'tsconfig.json',
      'jest.config.js',
      '.env',
      '.env.development',
      '.env.production',
      '.env.local',
      '.gitignore',
      '.editorconfig'
    ];

    // 默认需要复制的目录
    const defaultDirectories = [
      'mock',
      'public',
      'static',
      'assets',
      'scripts',
      'build',
      'config',
      'tests',
      '__tests__'
    ];

    this.logGray('   复制默认配置文件...');
    for (const configFile of defaultConfigFiles) {
      const sourceConfigPath = path.join(sourceDir, configFile);
      const targetConfigPath = path.join(targetDir, configFile);

      if (await fs.pathExists(sourceConfigPath)) {
        this.logGray(`   复制配置文件: ${configFile}`);
        await fs.copy(sourceConfigPath, targetConfigPath, { overwrite: true });
      }
    }

    this.logGray('   复制默认目录...');
    for (const directory of defaultDirectories) {
      const sourceDirPath = path.join(sourceDir, directory);
      const targetDirPath = path.join(targetDir, directory);

      if (await fs.pathExists(sourceDirPath)) {
        const stat = await fs.stat(sourceDirPath);
        if (stat.isDirectory()) {
          this.logGray(`   复制目录: ${directory}`);
          await fs.copy(sourceDirPath, targetDirPath, {
            overwrite: true,
            filter: (src) => {
              const fileName = path.basename(src);
              return !fileName.startsWith('.') &&
                     fileName !== 'node_modules' &&
                     !fileName.endsWith('.log');
            }
          });
        }
      }
    }
  }

  /**
   * 检测项目类型
   */
  async detectProject() {
    this.spinner = ora('检测项目类型...').start();

    try {
      const detectionResult = await this.detector.detectProject();

      this.spinner.succeed('项目类型检测完成');
      this.detector.printDetectionResult();

      // 如果是 Vue 3 项目，提示可能不需要迁移
      if (detectionResult.type === 'vue3') {
        console.log(chalk.yellow('\n⚠️  检测到 Vue 3 项目，可能不需要迁移'));
        const shouldContinue = this.options.force || false;
        if (!shouldContinue) {
          throw new Error('项目已经是 Vue 3，迁移已取消');
        }
      }

      // 如果是未知项目类型，使用默认配置
      if (detectionResult.type === 'unknown') {
        console.log(chalk.yellow('\n⚠️  未识别的项目类型，将使用默认配置'));
      }
    } catch (error) {
      this.spinner.fail('项目类型检测失败');
      throw error;
    }
  }

  /**
   * 执行迁移
   */
  async executeMigration() {
    this.log(chalk.blue('\n🔄 开始执行迁移...\n'))

    await this.step1_upgradePackageJson()
    await this.step3_migrateCode()

    await this.step4_logFailures()
    await this.step5_aiRepair()

    await this.step6_sassMigration()

    await this.step6_eslintFix()
    await this.step7_buildAndFix()

    await this.completeMigration()
  }

  async executePostMigrationSteps() {

  }

  /**
   * 步骤 1: 升级 package.json 依赖
   */
  async step1_upgradePackageJson() {
    const stepLabel = this.sourceToTargetMode ? '步骤 1/8' : '步骤 1/7';
    this.spinner = ora(`${stepLabel}: 处理 package.json...`).start();

    try {
      const packageJsonManager = new PackageJsonManager({
        sourceProjectPath: this.sourceProjectPath,
        targetProjectPath: this.targetProjectPath,
        workingPath: this.workingPath,
        sourceToTargetMode: this.sourceToTargetMode,
        migrationMode: true,
        preserveVue3Dependencies: true,
        enableThirdPartyMapping: true,
        verbose: this.options?.verbose ? this.options.verbose : false,
        dryRun: this.options?.dryRun ? this.options.dryRun : false
      });

      const result = await packageJsonManager.processPackageJson();
      this.stats.stepResults.packageUpgrade = result;

      this.completeStep();
      this.spinner.succeed(`${stepLabel}: package.json 处理完成`);

      if (this.options.verbose) {
        packageJsonManager.printMigrationReport(result);
      }
    } catch (error) {
      this.spinner.fail(`${stepLabel}: package.json 处理失败`);
      this.stats.errors.push({ step: 1, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 3: 批量迁移代码文件
   */
  async step3_migrateCode() {
    const stepLabel = this.sourceToTargetMode ? '步骤 3/8' : '步骤 3/7';
    this.spinner = ora(`${stepLabel}: 批量迁移代码文件...`).start();

    try {
      console.log(chalk.blue('🎯 正在选择最佳迁移策略...'));
      const migrator = new VueCodeMigrator(this.workingPath, {
        verbose: this.options.verbose
      });

      this.codeMigrator = migrator;

      const result = await migrator.migrate();

      this.stats.stepResults.codeMigration = {
        ...this.stats.stepResults.codeMigration,
        ...result
      };

      this.failedFiles = result.failedFiles || [];

      this.completeStep();

      this.spinner.succeed(`${stepLabel}: 代码文件迁移完成`);

      if (this.options.verbose) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }

      // 如果有失败文件，显示详细信息
      if (this.failedFiles.length > 0) {
        console.log(chalk.yellow(`⚠️  发现 ${this.failedFiles.length} 个转换失败的文件`));
        if (this.options.verbose) {
          this.failedFiles.slice(0, 5).forEach(file => {
            console.log(chalk.gray(`  ${file.file} - ${file.errorType}: ${file.error.substring(0, 80)}...`));
          });
          if (this.failedFiles.length > 5) {
            console.log(chalk.gray(`  ... 还有 ${this.failedFiles.length - 5} 个失败文件`));
          }
        }
      }
    } catch (error) {
      this.spinner.fail(`${stepLabel}: 代码文件迁移失败`);
      this.stats.errors.push({ step: 3, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 4: 记录失败文件
   */
  async step4_logFailures() {
    const stepLabel = this.sourceToTargetMode ? '步骤 4/8' : '步骤 4/7';
    this.spinner = ora(`${stepLabel}: 记录失败文件...`).start();

    try {
      const failureLogger = new FailureLogger(this.workingPath);
      await failureLogger.initialize();

      // 记录所有失败的文件
      if (this.failedFiles && this.failedFiles.length > 0) {
        for (const failedFile of this.failedFiles) {
          await failureLogger.logFailure(
            failedFile.file,
            new Error(failedFile.error),
            { step: 'code-migration' }
          );
        }
      }

      await failureLogger.saveFailures();
      this.stats.stepResults.failureLogging = {
        failedCount: this.failedFiles.length
      };
      this.completeStep();

      if (this.failedFiles.length > 0) {
        this.spinner.warn(`${stepLabel}: 记录了 ${this.failedFiles.length} 个失败文件`);
      } else {
        this.spinner.succeed(`${stepLabel}: 没有失败文件需要记录`);
      }
    } catch (error) {
      this.spinner.fail(`${stepLabel}: 失败文件记录失败`);
      this.stats.errors.push({ step: 4, error: error.message });
      // 这个步骤失败不应该中断整个流程
      this.completeStep();
    }
  }

  /**
   * 跳过步骤
   */
  skipStep(stepName) {
    console.log(chalk.gray(`⏭️  跳过: ${stepName}`));
    this.completeStep();
  }

  /**
   * 完成步骤
   */
  completeStep() {
    this.stats.completedSteps++;
  }

  /**
   * 步骤 5: AI 修复失败文件
   */
  async step5_aiRepair() {
    if (this.options.skipAIRepair) {
      this.skipStep('AI 修复');
      return;
    }

    const stepLabel = this.sourceToTargetMode ? '步骤 5/8' : '步骤 5/7';
    this.spinner = ora(`${stepLabel}: AI 修复失败文件...`).start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });
      if (!aiRepairer.isEnabled()) {
        this.spinner.warn(`${stepLabel}: AI 修复不可用（缺少 API Key）`);
        this.completeStep();
        return;
      }

      this.completeStep();

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.stats.errors.push({ step: 5, error: error.message });
      this.completeStep();
    }
  }

  /**
   * 步骤 6: ESLint 自动修复
   */
  async step6_eslintFix() {
    if (this.options.skipESLint) {
      this.skipStep('ESLint 修复');
      return;
    }

    const stepLabel = this.sourceToTargetMode ? '步骤 7/8' : '步骤 6/7';
    this.spinner = ora(`${stepLabel}: ESLint 自动修复...`).start();

    try {
      const eslintFixer = new ESLintFixer(this.workingPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        this.spinner.warn(`${stepLabel}: ESLint 不可用，跳过此步骤`);
        this.completeStep();
        return;
      }

      const result = await eslintFixer.fix();
      this.stats.stepResults.eslintFix = result;
      this.completeStep();

      this.spinner.succeed(`${stepLabel}: ESLint 修复完成`);

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  修复文件: ${result.filesFixed || 0}, 修复错误: ${result.errorsFixed || 0}`));
      }
    } catch (error) {
      this.spinner.fail(`${stepLabel}: ESLint 修复失败`);
      this.stats.errors.push({ step: 6, error: error.message });
      // ESLint 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  ESLint 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 6: Sass 语法迁移（源到目标模式专用）
   */
  async step6_sassMigration() {
    this.spinner = ora('步骤 6/8: Sass 语法迁移...').start();

    try {
      this.sassMigrator = new SassMigrator(this.targetProjectPath, {
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      });

      const result = await this.sassMigrator.migrate();
      this.stats.stepResults.sassMigration = result;
      this.completeStep();

      this.spinner.succeed('步骤 6/8: Sass 语法迁移完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  处理文件: ${result.processedFiles || 0}, 成功: ${result.processedFiles - result.errorFiles || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 6/8: Sass 语法迁移失败');
      this.stats.errors.push({ step: 6, error: error.message });
      // Sass 迁移失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  Sass 迁移失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 7: 构建项目并修复错误
   */
  async step7_buildAndFix() {
    if (this.options.skipBuild) {
      this.skipStep('构建修复');
      return;
    }

    const stepLabel = this.sourceToTargetMode ? '步骤 8/8' : '步骤 7/7';
    this.spinner = ora(`${stepLabel}: 构建项目并修复错误...`).start();

    try {
      const buildFixer = new BuildFixer(this.workingPath, {
        buildCommand: this.options.buildCommand
      });

      const result = await buildFixer.buildAndFix();
      this.stats.stepResults.buildFix = result;
      this.completeStep();

      if (result.success) {
        this.spinner.succeed(`${stepLabel}: 项目构建成功`);
      } else {
        this.spinner.warn(`${stepLabel}: 项目构建仍有问题，需要手动修复`);
      }

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  构建尝试: ${result.buildAttempts || 1}, 最终成功: ${result.success ? '是' : '否'}`));
      }
    } catch (error) {
      this.spinner.fail(`${stepLabel}: 构建修复失败`);
      this.stats.errors.push({ step: 7, error: error.message });
      // 构建失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  构建修复失败，可能需要手动处理'));
      this.completeStep();
    }
  }

  /**
   * 完成迁移
   */
  async completeMigration() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = this.stats.errors.length === 0;

    console.log('\n' + chalk.bold.green('🎉 Vue 2 到 Vue 3 迁移完成!'));
    this.printFinalStats();

    // 生成迁移报告
    await this.generateMigrationReport();

    // 显示后续建议
    this.printRecommendations();

    // 生成迁移指导文档
    await this.generateMigrationDoc();
  }

  /**
   * 打印最终统计
   */
  printFinalStats() {
    const duration = Math.round(this.stats.duration / 1000);

    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`完成步骤: ${this.stats.completedSteps}/${this.stats.totalSteps}`);
    console.log(`错误数量: ${this.stats.errors.length}`);

    if (this.stats.stepResults.codeMigration) {
      const cm = this.stats.stepResults.codeMigration;
      console.log(`代码文件: ${cm.success || 0} 成功, ${cm.failed || 0} 失败`);
    }

    if (this.stats.stepResults.buildFix?.success) {
      console.log(chalk.green('✅ 项目可以成功构建'));
    } else if (this.stats.stepResults.buildFix) {
      console.log(chalk.yellow('⚠️  项目构建可能仍有问题'));
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport() {
    const reportPath = path.join(this.workingPath, 'migration-report.json');

    const report = {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      options: this.options,
      stats: this.stats,
      success: this.stats.success,
      duration: this.stats.duration,
      recommendations: this.generateRecommendations()
    };

    if (!this.options.dryRun) {
      await fs.writeJson(reportPath, report, { spaces: 2 });
    }

    console.log(chalk.blue(`📄 迁移报告已生成: ${reportPath}`));
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.errors.length > 0) {
      recommendations.push('检查错误日志并手动修复剩余问题');
    }

    if (this.stats.stepResults.codeMigration?.failed > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    if (!this.stats.stepResults.buildFix?.success) {
      recommendations.push('运行构建命令检查剩余的构建错误');
    }

    recommendations.push('运行 npm install 安装新依赖');
    recommendations.push('运行测试确保功能正常');
    recommendations.push('检查 UI 组件是否正确迁移到 Element Plus');
    recommendations.push('更新文档和部署配置');

    return recommendations;
  }

  /**
   * 打印建议
   */
  printRecommendations() {
    const recommendations = this.generateRecommendations();

    if (recommendations.length > 0) {
      console.log('\n' + chalk.bold('💡 后续建议:'));
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

  /**
   * 生成迁移指导文档
   */
  async generateMigrationDoc() {
    try {
      console.log(chalk.blue('\n正在生成迁移指导文档...'));
      const docGenerator = new MigrationDocGenerator(this.workingPath, {}, {
        outputPath: path.join(this.workingPath, 'migration-guide.md'),
        includeUsageDetails: true,
        language: 'zh-CN'
      });

      const result = await docGenerator.generateMigrationGuide();

      if (result.success) {
        console.log(chalk.green(`✅ 迁移指导文档已生成: ${result.outputPath}`));
      } else {
        console.log(chalk.yellow('⚠️  迁移指导文档生成失败'));
      }
    } catch (error) {
      console.log(chalk.yellow('⚠️  迁移指导文档生成失败:'), error.message);
      // 不抛出错误，因为这不是核心功能
    }
  }

  /**
   * 显示迁移摘要
   */
  displayMigrationSummary() {
    if (this.quiet) {
      return; // 静默模式下不显示详细摘要
    }

    const summary = this.migrationContext.getStatusSummary();

    console.log(chalk.bold('\n📊 迁移摘要:'));
    console.log(chalk.gray('─'.repeat(50)));

    console.log(`项目类型: ${summary.project.detectedFramework || 'Unknown'}`);
    console.log(`完成阶段: ${summary.completedPhases}/${summary.totalPhases || 'N/A'}`);
    console.log(`处理文件: ${summary.filesProcessed} (修改: ${summary.filesModified})`);
    console.log(`错误数量: ${summary.errorCount}`);
    console.log(`警告数量: ${summary.warningCount}`);

    if (summary.aiCalls > 0) {
      console.log(`AI 调用: ${summary.aiCalls} (成功率: ${summary.aiSuccessRate.toFixed(1)}%)`);
    }

    console.log(`构建状态: ${summary.buildStatus}`);
    console.log(`总耗时: ${(summary.duration / 1000).toFixed(2)}s`);

    if (summary.errorCount > 0) {
      console.log(chalk.yellow('\n⚠️  存在错误，请检查日志文件'));
    }

    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * 重写父类的 execute 方法
   */
  async execute() {
    return await this.migrate();
  }

  /**
   * 重写父类的事件监听设置
   */
  onSetupEventListeners() {
    // 监听阶段变化
    this.subscribe('phase:start', (data) => {
      this.log(chalk.blue(`🔄 开始阶段: ${data.phase}`));
    });

    this.subscribe('phase:complete', (data) => {
      this.log(chalk.green(`✅ 完成阶段: ${data.phase}`));
    });

    this.subscribe('phase:failed', (data) => {
      this.logAlways(chalk.red(`❌ 阶段失败: ${data.phase} - ${data.error.message}`));
    });

    // 监听文件处理
    this.subscribe('file:processed', (data) => {
      if (this.options.verbose) {
        console.log(chalk.gray(`   处理文件: ${data.file}`));
      }
    });

    // 监听 AI 调用
    this.subscribe('ai:call', (data) => {
      if (this.options.verbose) {
        const status = data.callInfo.success ? '✅' : '❌';
        console.log(chalk.gray(`   AI 调用 ${status}: ${data.callInfo.type || 'unknown'}`));
      }
    });
  }

}

module.exports = AutoMigrator;
