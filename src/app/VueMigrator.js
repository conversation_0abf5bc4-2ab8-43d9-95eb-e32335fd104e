const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const VueCodeMigrator = require('../frameworks/vue/VueCodeTransformer');

/**
 * Vue 迁移器
 * 专门负责 Vue 2 到 Vue 3 的代码迁移
 * 封装了 VueCodeMigrator 的功能，提供更高层次的迁移编排
 */
class VueMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = options;

    // 确定工作路径
    this.workingPath = this.projectPath;

    this.spinner = null;
    this.failedFiles = [];

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      success: false,
      errors: [],
      stepResults: {}
    };

    // 输出控制
    this.quiet = options.quiet || false;
    this.verbose = options.verbose || false;
  }

  /**
   * 条件输出日志 - 仅在非静默模式下输出
   */
  log(message, ...args) {
    if (!this.quiet) {
      console.log(message, ...args);
    }
  }

  /**
   * 执行 Vue 代码迁移
   */
  async migrate() {
    this.spinner = ora('Vue 代码迁移: 批量迁移代码文件...').start();

    try {
      console.log(chalk.blue('🎯 正在选择最佳迁移策略...'));
      const migrator = new VueCodeMigrator(this.workingPath, {
        verbose: this.options.verbose,
        isOutputMode: this.options.isOutputMode || false,
        outputPath: this.options.outputPath || '',
        srcDir: this.options.srcDir || 'src',
        outputSrcDir: this.options.outputSrcDir || 'src',
        includePatterns: this.options.includePatterns || ['**/*.vue', '**/*.js', '**/*.ts'],
        excludePatterns: this.options.excludePatterns || ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.*/**'],
        buildCommand: this.options.buildCommand || 'npm run build'
      });

      this.codeMigrator = migrator;

      const result = await migrator.migrate();

      this.stats.stepResults.codeMigration = {
        ...this.stats.stepResults.codeMigration,
        ...result
      };

      this.failedFiles = result.failedFiles || [];

      this.spinner.succeed('Vue 代码迁移: 代码文件迁移完成');

      if (this.verbose) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }

      // 如果有失败文件，显示详细信息
      if (this.failedFiles.length > 0) {
        console.log(chalk.yellow(`⚠️  发现 ${this.failedFiles.length} 个转换失败的文件`));
        if (this.verbose) {
          this.failedFiles.slice(0, 5).forEach(file => {
            console.log(chalk.gray(`  ${file.file} - ${file.errorType}: ${file.error.substring(0, 80)}...`));
          });
          if (this.failedFiles.length > 5) {
            console.log(chalk.gray(`  ... 还有 ${this.failedFiles.length - 5} 个失败文件`));
          }
        }
      }

      return {
        success: true,
        failedFiles: this.failedFiles,
        stats: this.stats
      };
    } catch (error) {
      this.spinner.fail('Vue 代码迁移: 代码文件迁移失败');
      this.stats.errors.push({ step: 'migrate', error: error.message });
      throw error;
    }
  }
}

module.exports = VueMigrator;
