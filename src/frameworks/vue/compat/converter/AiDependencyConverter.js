const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../../../../ai/AiService');
const ComponentUsageAnalyzer = require('../../third-party/ComponentUsageAnalyzer');
const { getMigratorDocPath, getMigratorDocContent } = require('../../third-party/MigrationDocReader');

/**
 * AI 依赖转换器
 * 使用 AI 能力转换没有 Vue 3 版本的依赖库
 */
class AiDependencyConverter extends AIService {
  constructor(options = {}) {
    super(options);
    this.projectPath = options.projectPath || process.cwd();
    this.analyzer = new ComponentUsageAnalyzer(this.projectPath, options);
    this.conversionResults = [];
  }

  /**
   * 分析项目并转换不兼容的依赖
   * @returns {Promise<Object>} 转换结果
   */
  async convertIncompatibleDependencies(incompatibleDeps) {
    if (!this.enabled) {
      console.log(chalk.yellow('⚠️ AI 服务未启用，无法转换不兼容的依赖'));
      return { success: false, reason: 'AI service disabled' };
    }

    console.log(chalk.blue(`\n🔍 分析项目中的依赖组件使用情况...`));

    // 使用 ComponentUsageAnalyzer 分析项目中的组件使用
    const analysisResult = await this.analyzer.analyze();

    console.log(chalk.blue(`\n🤖 开始使用 AI 转换不兼容的依赖库...`));
    console.log(chalk.gray(`找到 ${incompatibleDeps.length} 个不兼容的依赖需要转换`));

    for (const dep of incompatibleDeps) {
      try {
        await this.convertSingleDependency(dep, analysisResult);
      } catch (error) {
        console.error(chalk.red(`❌ 转换依赖失败: ${dep.name}`), error.message);
        this.stats.failed++;
      }

      this.stats.attempted++;
    }

    this.printConversionStats();
    return {
      success: true,
      results: this.conversionResults,
      stats: this.getStats()
    };
  }

  /**
   * 转换单个依赖库
   */
  async convertSingleDependency(dependency, analysisResult) {
    console.log(chalk.gray(`🔧 转换: ${dependency.name}...`));

    // 查找该依赖在代码中的使用位置
    const usageLocations = analysisResult.usageInCode.filter(
      usage => usage.dependency === dependency.name
    );

    if (usageLocations.length === 0) {
      console.log(chalk.yellow(`⚠️ 未找到 ${dependency.name} 的使用位置，跳过转换`));
      this.stats.skipped++;
      return;
    }

    console.log(chalk.gray(`找到 ${usageLocations.length} 个使用位置`));

    // 获取依赖的迁移文档
    const docPath = getMigratorDocPath(dependency.name);
    const docContent = docPath ? getMigratorDocContent(dependency.name) : null;

    // 转换每个使用位置
    let successCount = 0;
    for (const usage of usageLocations) {
      try {
        const filePath = path.join(this.projectPath, usage.file);
        const fileContent = await fs.readFile(filePath, 'utf8');

        // 准备执行转换
        const conversionResult = await this.convertUsage(
          fileContent,
          usage,
          dependency,
          docContent
        );

        if (conversionResult.success) {
          // 备份原始文件
          await this.backupFile(filePath);

          // 写入转换后的内容
          await fs.writeFile(filePath, conversionResult.content, 'utf8');

          successCount++;
          console.log(chalk.green(`✅ 成功转换 ${usage.file} 中的使用`));
        } else {
          console.log(chalk.yellow(`⚠️ 转换失败: ${usage.file}`));
        }
      } catch (error) {
        console.error(chalk.red(`❌ 处理文件失败: ${usage.file}`), error.message);
      }
    }

    if (successCount > 0) {
      this.stats.success++;
      this.conversionResults.push({
        dependency: dependency.name,
        success: true,
        locationsConverted: successCount,
        totalLocations: usageLocations.length
      });
    } else {
      this.stats.failed++;
      this.conversionResults.push({
        dependency: dependency.name,
        success: false,
        error: '无法转换任何使用位置'
      });
    }
  }

  /**
   * 转换单个组件使用
   */
  async convertUsage(fileContent, usage, dependency, docContent) {
    // 获取包含使用代码的片段
    const lines = fileContent.split('\n');
    const contextStart = Math.max(0, usage.line - 10);
    const contextEnd = Math.min(lines.length, usage.line + 10);
    const codeSnippet = lines.slice(contextStart, contextEnd).join('\n');

    // 生成转换提示
    const prompt = this.generateConversionPrompt(
      codeSnippet,
      usage,
      dependency,
      docContent,
      path.extname(usage.file)
    );

    // 调用 AI 进行转换
    const convertedCode = await this.callAI(prompt);

    // 替换原始代码
    if (convertedCode && convertedCode.trim().length > 0) {
      const newContent = fileContent.replace(
        codeSnippet,
        convertedCode.trim()
      );

      return {
        success: true,
        content: newContent
      };
    }

    return {
      success: false,
      error: '生成的代码无效'
    };
  }

  /**
   * 生成转换提示
   */
  generateConversionPrompt(codeSnippet, usage, dependency, docContent, fileExtension) {
    let prompt = `将以下使用 Vue 2 的 ${dependency.name} 依赖代码转换为 Vue 3 兼容的代码。

**依赖信息**:
- 名称: ${dependency.name}
- 版本: ${dependency.version}
- 问题: 该依赖库没有 Vue 3 的兼容版本，需要替换或修改使用方式

**代码上下文**:
\`\`\`${fileExtension.slice(1) || 'js'}
${codeSnippet}
\`\`\`

**使用位置**: 第 ${usage.line} 行，内容: \`${usage.content}\`

**转换要求**:
1. 保持功能与原代码一致
2. 使用 Vue 3 兼容的语法和 API
3. 如果需要，建议合适的替代依赖库
4. 确保转换后的代码可以正常工作
`;

    // 如果有迁移文档，添加到提示中
    if (docContent) {
      prompt += `\n\n**迁移文档参考**:\n${docContent}\n`;
    }

    prompt += `\n请提供完整的转换后代码片段，确保它可以替换原始代码片段。`;

    return prompt;
  }

  /**
   * 打印转换统计
   */
  printConversionStats() {
    console.log('\n' + chalk.bold('🤖 AI 依赖转换统计:'));
    console.log(`尝试转换: ${this.stats.attempted} 个依赖`);
    console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));
    console.log(chalk.yellow(`⏩ 跳过: ${this.stats.skipped} 个`));

    if (this.stats.attempted > 0) {
      const successRate = ((this.stats.success / this.stats.attempted) * 100).toFixed(1);
      console.log(chalk.bold(`成功率: ${successRate}%`));
    }
  }
}

module.exports = AiDependencyConverter;
