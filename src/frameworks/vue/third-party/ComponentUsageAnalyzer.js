const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const glob = require('glob');
const { getMigratorDocPath, getAllMigratorDocs } = require('./MigrationDocReader');

/**
 * 组件使用分析器
 * 分析项目中使用了哪些需要迁移的组件，扫描 package.json 和代码文件
 */
class ComponentUsageAnalyzer {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
      excludePatterns: ['node_modules/**', 'dist/**', 'build/**', '**/*.test.js', '**/*.spec.js'],
      ...options
    };

    this.packageJsonPath = path.join(this.projectPath, 'package.json');
    this.analysisResult = {
      dependencies: [],
      usageInCode: [],
      migrationDocs: [],
      summary: {
        totalDependencies: 0,
        dependenciesWithDocs: 0,
        dependenciesWithoutDocs: 0,
        filesScanned: 0,
        usageFound: 0
      }
    };
  }

  /**
   * 执行完整的组件使用分析
   */
  async analyze() {
    try {
      console.log(chalk.blue('🔍 开始分析项目中的组件使用情况...'));
      console.log(chalk.gray(`项目路径: ${this.projectPath}`));

      // 1. 分析 package.json 中的依赖
      await this.analyzeDependencies();

      // 2. 扫描代码文件中的使用情况
      await this.analyzeCodeUsage();

      // 3. 匹配迁移文档
      await this.matchMigrationDocs();

      // 4. 生成分析摘要
      this.generateSummary();

      console.log(chalk.green('✅ 组件使用分析完成'));
      this.printAnalysisResult();

      return this.analysisResult;
    } catch (error) {
      console.error(chalk.red('❌ 组件使用分析失败:'), error.message);
      throw error;
    }
  }

  /**
   * 分析 package.json 中的依赖
   */
  async analyzeDependencies() {
    if (!await fs.pathExists(this.packageJsonPath)) {
      throw new Error(`package.json 不存在: ${this.packageJsonPath}`);
    }

    const packageJson = await fs.readJson(this.packageJsonPath);
    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    console.log(chalk.gray(`正在分析 ${Object.keys(allDeps).length} 个依赖...`));

    // 过滤出可能需要迁移的依赖（Vue 相关、UI 库等）
    const migrationCandidates = this.filterMigrationCandidates(allDeps);

    this.analysisResult.dependencies = migrationCandidates.map(dep => ({
      name: dep.name,
      version: dep.version,
      type: dep.type,
      category: this.categorizeDependency(dep.name),
      hasDoc: getMigratorDocPath(dep.name) !== null
    }));

    console.log(chalk.gray(`找到 ${this.analysisResult.dependencies.length} 个可能需要迁移的依赖`));
  }

  /**
   * 过滤出可能需要迁移的依赖
   */
  filterMigrationCandidates(dependencies) {
    const candidates = [];
    const migrationKeywords = [
      'vue', 'element', 'vuex', 'router', 'draggable', 'chart', 'editor',
      'calendar', 'tree', 'table', 'form', 'ui', 'component'
    ];

    for (const [name, version] of Object.entries(dependencies)) {
      const isCandidate = migrationKeywords.some(keyword =>
        name.toLowerCase().includes(keyword)
      );

      const hasDoc = getMigratorDocPath(name) !== null;

      if (isCandidate || hasDoc) {
        candidates.push({
          name,
          version,
          type: this.getDependencyType(name, dependencies)
        });
      }
    }

    return candidates;
  }

  /**
   * 获取依赖类型
   */
  getDependencyType(depName, allDeps) {
    // 这里简化处理，实际项目中可能需要更复杂的逻辑
    return 'dependencies'; // 或 'devDependencies'
  }

  /**
   * 对依赖进行分类
   */
  categorizeDependency(depName) {
    const categories = {
      'vue-core': ['vue', 'vue-router', 'vuex', 'pinia'],
      'ui-library': ['element-ui', 'element-plus', 'ant-design-vue', 'vuetify'],
      'chart': ['echarts', 'v-charts', 'vue-echarts'],
      'editor': ['tinymce', 'wangeditor', 'quill'],
      'utility': ['vue-draggable', 'vue-count-to', 'vue-uuid'],
      'other': []
    };

    for (const [category, deps] of Object.entries(categories)) {
      if (deps.some(dep => depName.includes(dep))) {
        return category;
      }
    }

    return 'other';
  }

  /**
   * 扫描代码文件中的使用情况
   */
  async analyzeCodeUsage() {
    console.log(chalk.gray('正在扫描代码文件中的组件使用情况...'));

    const files = await this.getFilesToScan();
    console.log(chalk.gray(`找到 ${files.length} 个文件需要扫描`));

    this.analysisResult.summary.filesScanned = files.length;

    for (const filePath of files) {
      await this.scanFileForUsage(filePath);
    }

    console.log(chalk.gray(`在 ${this.analysisResult.usageInCode.length} 个位置找到组件使用`));
  }

  /**
   * 获取需要扫描的文件列表
   */
  async getFilesToScan() {
    const files = [];

    for (const pattern of this.options.includePatterns) {
      const matchedFiles = glob.sync(pattern, {
        cwd: this.projectPath,
        ignore: this.options.excludePatterns,
        absolute: true
      });
      files.push(...matchedFiles);
    }

    // 去重
    return [...new Set(files)];
  }

  /**
   * 扫描单个文件中的组件使用
   */
  async scanFileForUsage(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const relativePath = path.relative(this.projectPath, filePath);

      // 扫描依赖中的组件使用
      for (const dep of this.analysisResult.dependencies) {
        const usages = this.findComponentUsageInContent(content, dep.name, relativePath);
        this.analysisResult.usageInCode.push(...usages);
      }
    } catch (error) {
      console.warn(chalk.yellow(`警告: 无法读取文件 ${filePath}: ${error.message}`));
    }
  }

  /**
   * 在文件内容中查找组件使用
   */
  findComponentUsageInContent(content, depName, filePath) {
    const usages = [];
    const lines = content.split('\n');

    // 简化的模式匹配，实际项目中可能需要更复杂的 AST 分析
    const patterns = [
      new RegExp(`import.*from\\s+['"]${depName}['"]`, 'g'),
      new RegExp(`require\\(['"]${depName}['"]\\)`, 'g'),
      new RegExp(`@import\\s+['"]${depName}`, 'g')
    ];

    lines.forEach((line, index) => {
      patterns.forEach(pattern => {
        if (pattern.test(line)) {
          usages.push({
            dependency: depName,
            file: filePath,
            line: index + 1,
            content: line.trim(),
            type: this.getUsageType(line)
          });
        }
      });
    });

    return usages;
  }

  /**
   * 获取使用类型
   */
  getUsageType(line) {
    if (line.includes('import')) return 'import';
    if (line.includes('require')) return 'require';
    if (line.includes('@import')) return 'css-import';
    return 'unknown';
  }

  /**
   * 匹配迁移文档
   */
  async matchMigrationDocs() {
    console.log(chalk.gray('正在匹配迁移文档...'));

    const allDocs = getAllMigratorDocs();

    for (const dep of this.analysisResult.dependencies) {
      const docInfo = allDocs.find(doc => doc.name === dep.name);
      if (docInfo) {
        this.analysisResult.migrationDocs.push({
          dependency: dep.name,
          docPath: docInfo.path,
          hasUsage: this.analysisResult.usageInCode.some(usage => usage.dependency === dep.name)
        });
      }
    }

    console.log(chalk.gray(`找到 ${this.analysisResult.migrationDocs.length} 个迁移文档`));
  }

  /**
   * 生成分析摘要
   */
  generateSummary() {
    const summary = this.analysisResult.summary;

    summary.totalDependencies = this.analysisResult.dependencies.length;
    summary.dependenciesWithDocs = this.analysisResult.dependencies.filter(dep => dep.hasDoc).length;
    summary.dependenciesWithoutDocs = summary.totalDependencies - summary.dependenciesWithDocs;
    summary.usageFound = this.analysisResult.usageInCode.length;
  }

  /**
   * 打印分析结果
   */
  printAnalysisResult() {
    const { summary } = this.analysisResult;

    console.log('\n' + chalk.bold('📊 组件使用分析结果:'));
    console.log(`总依赖数: ${summary.totalDependencies}`);
    console.log(`有迁移文档: ${summary.dependenciesWithDocs}`);
    console.log(`无迁移文档: ${summary.dependenciesWithoutDocs}`);
    console.log(`扫描文件数: ${summary.filesScanned}`);
    console.log(`找到使用: ${summary.usageFound} 处`);

    if (this.analysisResult.dependencies.length > 0) {
      console.log('\n' + chalk.bold('需要迁移的依赖:'));
      this.analysisResult.dependencies.forEach(dep => {
        const hasUsage = this.analysisResult.usageInCode.some(usage => usage.dependency === dep.name);
        const status = dep.hasDoc ? '📖' : '❓';
        const usage = hasUsage ? '✅' : '⚪';
        console.log(`  ${status} ${usage} ${dep.name}@${dep.version} (${dep.category})`);
      });
    }
  }

  /**
   * 获取分析结果
   */
  getAnalysisResult() {
    return this.analysisResult;
  }
}

module.exports = ComponentUsageAnalyzer;
