/**
 * 定制提示词系统
 * 基于常见问题和最佳实践优化 AI 响应
 */

class CustomPrompts {
  constructor() {
    this.commonIssues = this.loadCommonIssues();
    this.bestPractices = this.loadBestPractices();
  }

  /**
   * 加载常见问题模式
   */
  loadCommonIssues() {
    return {
      // Vue 3 语法问题
      vue3Syntax: {
        patterns: [
          'this.$router',
          'this.$store',
          'this.$refs',
          'Vue.extend',
          'new Vue',
          '@vue/composition-api'
        ],
        solutions: [
          '使用 useRouter() 替代 this.$router',
          '使用 useStore() 替代 this.$store',
          '使用 ref() 和 getCurrentInstance() 处理 refs',
          '使用 defineComponent() 替代 Vue.extend',
          '使用 createApp() 替代 new Vue',
          '移除 @vue/composition-api，使用内置 API'
        ]
      },

      // Element UI 到 Element Plus
      elementMigration: {
        patterns: [
          'element-ui',
          'el-form-item',
          'el-button',
          'MessageBox',
          'Message',
          '$message',
          '$confirm'
        ],
        solutions: [
          '更新为 element-plus',
          '检查 el-form-item 属性变化',
          '检查 el-button 属性变化',
          '使用 ElMessageBox 替代 MessageBox',
          '使用 ElMessage 替代 Message',
          '导入并使用 ElMessage',
          '导入并使用 ElMessageBox.confirm'
        ]
      },

      // 路由相关问题
      routerIssues: {
        patterns: [
          'router.push',
          'router.replace',
          '$route.params',
          '$route.query',
          'beforeRouteEnter',
          'beforeRouteUpdate'
        ],
        solutions: [
          '在 setup() 中使用 router.push()',
          '在 setup() 中使用 router.replace()',
          '使用 route.params',
          '使用 route.query',
          '使用 onBeforeRouteEnter',
          '使用 onBeforeRouteUpdate'
        ]
      },

      // 响应式数据问题
      reactivityIssues: {
        patterns: [
          'data()',
          'computed:',
          'watch:',
          'this.',
          'Vue.set',
          'Vue.delete'
        ],
        solutions: [
          '使用 ref() 或 reactive() 定义响应式数据',
          '使用 computed() 函数',
          '使用 watch() 或 watchEffect() 函数',
          '在 setup() 中避免使用 this',
          '直接赋值，Vue 3 响应式系统会自动处理',
          '直接删除属性，Vue 3 响应式系统会自动处理'
        ]
      }
    };
  }

  /**
   * 加载最佳实践
   */
  loadBestPractices() {
    return {
      // 文件修复优先级
      filePriority: [
        '优先修复 .vue 组件文件',
        '其次修复 .js/.ts 逻辑文件',
        '最后考虑配置文件',
        '避免修改 node_modules',
        '避免修改 package.json（除非必要）',
        '避免修改 vue.config.js（除非必要）'
      ],

      // 代码修复原则
      codeFixPrinciples: [
        '保持代码简洁和可读性',
        '使用 Vue 3 推荐的组合式 API',
        '确保向后兼容性',
        '添加必要的类型注解',
        '遵循项目现有的代码风格',
        '添加适当的错误处理'
      ],

      // 错误分析方法
      errorAnalysis: [
        '首先识别错误类型（语法、运行时、构建）',
        '定位错误的具体文件和行号',
        '分析错误的根本原因',
        '考虑最小化修改方案',
        '验证修复方案的可行性',
        '确保不引入新的问题'
      ]
    };
  }

  /**
   * 根据错误内容生成定制提示词
   */
  generateCustomPrompt(errorMessage, filePath, context = {}) {
    const customInstructions = [];

    // 分析错误类型并添加相应的指导
    const issueType = this.detectIssueType(errorMessage, filePath);
    if (issueType) {
      customInstructions.push(this.getIssueSpecificGuidance(issueType));
    }

    // 添加文件类型特定的指导
    const fileGuidance = this.getFileTypeGuidance(filePath);
    if (fileGuidance) {
      customInstructions.push(fileGuidance);
    }

    // 添加通用最佳实践
    customInstructions.push(this.getGeneralBestPractices());

    return customInstructions.join('\n\n');
  }

  /**
   * 检测问题类型
   */
  detectIssueType(errorMessage, filePath) {
    const message = errorMessage.toLowerCase();

    // Vue 3 语法问题
    if (this.commonIssues.vue3Syntax.patterns.some(pattern =>
        message.includes(pattern.toLowerCase()))) {
      return 'vue3Syntax';
    }

    // Element UI 迁移问题
    if (this.commonIssues.elementMigration.patterns.some(pattern =>
        message.includes(pattern.toLowerCase()))) {
      return 'elementMigration';
    }

    // 路由问题
    if (this.commonIssues.routerIssues.patterns.some(pattern =>
        message.includes(pattern.toLowerCase()))) {
      return 'routerIssues';
    }

    // 响应式数据问题
    if (this.commonIssues.reactivityIssues.patterns.some(pattern =>
        message.includes(pattern.toLowerCase()))) {
      return 'reactivityIssues';
    }

    return null;
  }

  /**
   * 获取问题特定的指导
   */
  getIssueSpecificGuidance(issueType) {
    const issue = this.commonIssues[issueType];
    if (!issue) return '';

    return `**${issueType} 问题检测到，常见解决方案**：
${issue.solutions.map(solution => `- ${solution}`).join('\n')}`;
  }

  /**
   * 获取文件类型特定的指导
   */
  getFileTypeGuidance(filePath) {
    const ext = filePath.split('.').pop();

    switch (ext) {
      case 'vue':
        return `**Vue 组件修复指导**：
- 优先使用组合式 API (setup())
- 确保正确导入 Vue 3 API
- 检查模板语法兼容性
- 更新生命周期钩子名称`;

      case 'js':
      case 'ts':
        return `**JavaScript/TypeScript 文件修复指导**：
- 更新 Vue 3 相关导入
- 使用新的 API 语法
- 确保类型定义正确
- 检查依赖兼容性`;

      default:
        if (filePath.includes('config')) {
          return `**配置文件修复指导**：
- ⚠️ 配置文件修改风险较高
- 优先考虑代码层面的解决方案
- 只在确实必要时才修改配置
- 确保配置语法正确`;
        }
        return '';
    }
  }

  /**
   * 获取通用最佳实践
   */
  getGeneralBestPractices() {
    return `**通用最佳实践**：
${this.bestPractices.codeFixPrinciples.map(principle => `- ${principle}`).join('\n')}

**修复优先级**：
${this.bestPractices.filePriority.map(priority => `- ${priority}`).join('\n')}`;
  }

  /**
   * 生成运行时错误修复提示词
   */
  generateRuntimeErrorPrompt(errorMessage, filePath, componentPath) {
    const customGuidance = this.generateCustomPrompt(errorMessage, filePath);

    return `${customGuidance}

**运行时错误特别注意**：
- 检查组件的响应式数据定义
- 确保正确的生命周期钩子使用
- 验证组件导入和导出
- 检查模板中的指令和事件绑定

**推荐的组件文件路径**：
${componentPath ? `- ${componentPath}` : '- 根据错误信息推断'}`;
  }
}

module.exports = CustomPrompts;
