const path = require('path');
const PromptResponseHandler = require('./PromptResponseHandler');
const CustomPrompts = require('./CustomPrompts');

/**
 * PromptBuilder - 提示词构建器
 *
 * 负责：
 * - 根据不同场景构建合适的提示词
 * - 支持模板化和参数化
 * - 提供策略模式支持不同类型的提示词
 * - 统一管理所有提示词模板
 */
class PromptBuilder {
  constructor(toolRegistry, options = {}) {
    this.toolRegistry = toolRegistry;
    this.options = {
      maxOutputLength: 5000,
      includeContext: true,
      ...options
    };

    // 初始化响应处理器
    this.responseHandler = new PromptResponseHandler(this.options);

    // 初始化定制提示词系统
    this.customPrompts = new CustomPrompts();

    // 提示词模板
    this.templates = {
      toolCall: this.getToolCallTemplate(),
      fileFix: this.getFileFixTemplate(),
      webpackFix: this.getWebpackFixTemplate(),
      vueFix: this.getVueFixTemplate(),
      generalFix: this.getGeneralFixTemplate(),
      runtimeErrorFix: this.getRuntimeErrorFixTemplate()
    };
  }

  buildToolCallPrompt(filePath, buildOutput, context = {}) {
    const fileExtension = path.extname(filePath);
    const truncatedOutput = this.truncateOutput(buildOutput);

    let template = this.templates.toolCall;

    // 根据文件类型选择特定模板
    if (filePath.includes('webpack') || filePath.includes('vue.config')) {
      template = this.getWebpackToolCallTemplate();
    } else if (fileExtension === '.vue') {
      template = this.getVueToolCallTemplate();
    } else if (fileExtension === 'main.js') {
      template = this.getVueFixTemplate() + "\n 请使用 Vue 3 的写法重写当前文件。\n"
    } else {
      template = this.getVueFixTemplate()
    }

    const suggestions = context.suggestions || '';

    return template
      .replace('{filePath}', filePath)
      .replace('{buildOutput}', truncatedOutput)
      .replace('{toolsDescription}', this.toolRegistry.getToolsDescription())
      .replace('{context}', this.formatContext(context))
      .replace('{attemptNumber}', context.attemptNumber || 1)
      .replace('{previousAttempts}', this.formatPreviousAttempts(context.previousAttempts || []))
      .replace('{suggestions}', suggestions);
  }

  /**
   * 构建文件修复提示词
   */
  buildFileFixPrompt(filePath, fileContent, buildOutput, contextFiles = {}, context = {}, suggestions) {
    const fileExtension = path.extname(filePath);
    const truncatedOutput = this.truncateOutput(buildOutput);

    let template;

    // 根据文件类型选择特定模板
    if (filePath.includes('webpack') || filePath.includes('vue.config')) {
      template = this.templates.webpackFix;
    } else if (fileExtension === '.vue') {
      template = this.templates.vueFix;
    } else {
      template = this.templates.generalFix;
    }

    return template
        .replace('{filePath}', filePath)
        .replace('{fileContent}', fileContent)
        .replace('{buildOutput}', truncatedOutput)
        .replace('{contextFiles}', this.formatContextFiles(contextFiles))
        .replace('{context}', this.formatContext(context))
        .replace('{attemptNumber}', context.attemptNumber || 1)
        .replace('{previousAttempts}', this.formatPreviousAttempts(context.previousAttempts || []))
        .replace('{fileIndex}', context.fileIndex || 1)
        .replace('{totalFiles}', context.totalFiles || 1)

      + `**修复建议**：\n${JSON.stringify(suggestions, null, 2)}`
  }

  /**
   * 构建带上下文的文件修复提示词
   */
  buildFileFixPromptWithContext(filePath, fileContent, buildOutput, contextFiles, context = {}, suggestions) {
    return this.buildFileFixPrompt(filePath, fileContent, buildOutput, contextFiles, {
      ...context,
      includeContext: true
    }, suggestions);
  }

  /**
   * 构建运行时错误修复提示词
   */
  buildRuntimeErrorFixPrompt(fileName, fileContent, errorContext, context = {}) {
    const template = this.templates.runtimeErrorFix;

    // 生成定制提示词
    const customGuidance = this.customPrompts.generateRuntimeErrorPrompt(
      errorContext.message || '',
      fileName,
      context.suggestedFiles?.[0]
    );

    return template
      .replace('{fileName}', fileName)
      .replace('{fileContent}', fileContent)
      .replace('{errorMessage}', errorContext.message || '')
      .replace('{errorStack}', errorContext.stack || '')
      .replace('{componentTrace}', this.formatComponentTrace(errorContext.componentTrace || []))
      .replace('{errorType}', errorContext.type || 'runtime')
      .replace('{context}', this.formatContext(context))
      .replace('{lineNumber}', errorContext.lineNumber || 0)
      .replace('{columnNumber}', errorContext.columnNumber || 0)
      .replace('{customGuidance}', customGuidance);
  }

  getToolCallTemplate() {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请分析以下构建错误，确定需要读取哪些相关文件来理解上下文，然后生成相应的工具调用。

**目标文件**: {filePath}
**尝试次数**: 第 {attemptNumber} 次

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**可用工具**：
{toolsDescription}

**上下文信息**：
{context}

**之前的尝试**：
{previousAttempts}

**智能修复建议**：
{suggestions}

**任务**：
1. 分析错误信息，理解问题的根本原因
2. 确定需要读取哪些相关文件来获得足够的上下文信息
3. 如果是大小写敏感路径错误，直接使用 rename_file 工具修复
4. 生成合适的工具调用

**工具调用原则**：
- 读取与错误直接相关的文件
- 读取可能包含相关配置或依赖的文件
- 读取可能提供修复线索的文件
- 避免读取过多无关文件

**响应格式**：
请使用以下 JSON 格式返回工具调用：

\`\`\`json
{
  "tool_calls": [
    {
      "name": "read_file",
      "parameters": {
        "file_path": "src/components/Example.vue"
      }
    },
    {
      "name": "list_files", 
      "parameters": {
        "directory": "src/utils",
        "pattern": "*.js"
      }
    }
  ],
  "reasoning": "说明为什么需要读取这些文件"
}
\`\`\`

请仔细分析错误信息，生成合适的工具调用。`;
  }

  /**
   * 获取文件修复模板
   */
  getFileFixTemplate() {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请根据构建错误和上下文信息修复以下文件。

目标项目的技术栈是：Webpack 5 + Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**目标文件**: {filePath} (第 {fileIndex}/{totalFiles} 个文件)
**尝试次数**: 第 {attemptNumber} 次

**当前文件内容**：
\`\`\`
{fileContent}
\`\`\`

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**相关上下文文件**：
{contextFiles}

**上下文信息**：
{context}

**之前的尝试**：
{previousAttempts}

**修复要求**：
1. 仔细分析错误信息和文件内容
2. 基于上下文文件理解项目结构和依赖关系
3. 提供完整的修复后文件内容
4. 确保修复后的代码符合 Vue 3 规范
5. 保持代码的功能完整性
6. SCSS/Sass 文件注意事项：
   - @use 规则必须放在文件最前面
   - @forward 规则必须放在 @use 之后
   - @import 规则应该放在 @use 和 @forward 之后
   - 其他所有规则（包括样式规则）必须放在 @use、@forward 和 @import 之后

**响应格式**：
请使用以下格式返回修复结果：

\`\`\`xml
${this.responseHandler.generateFormatExample('fix_result', '修复后的完整文件内容', '简要说明所做的修改')}
\`\`\`

请提供完整的修复后文件内容，不要省略任何部分。`;
  }

  /**
   * 获取Webpack配置修复模板
   */
  getWebpackFixTemplate() {
    return `你是一个专业的 Webpack 和 Vue 3 配置专家。

**重要提醒**：配置文件修改风险较高，请优先考虑以下替代方案：
1. 修改源代码文件来适配当前配置
2. 调整组件导入方式
3. 修复代码语法问题
4. 只有在确实必要时才修改配置文件

目标项目的技术栈是：Webpack 5 + Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**配置文件**: {filePath}
**尝试次数**: 第 {attemptNumber} 次

**当前配置内容**：
\`\`\`
{fileContent}
\`\`\`

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**相关上下文文件**：
{contextFiles}

**上下文信息**：
{context}

**之前的尝试**：
{previousAttempts}

**Webpack 5 + Vue 3 修复指南**：
1. **插件配置**: 确保使用 Vue 3 兼容的插件
2. **模块解析**: 配置正确的 resolve 规则
3. **Polyfill**: Webpack 5 不再自动提供 Node.js polyfill
4. **加载器**: 使用 Vue 3 兼容的 loader
5. **优化配置**: 适配 Webpack 5 的优化选项

**常见修复模式**：
- 移除过时的插件和配置
- 添加必要的 polyfill
- 更新插件配置语法
- 修复模块解析路径
- 安装插件：npm install @vue/preload-webpack-plugin
- 正确的使用 Preload 插件：const PreloadPlugin = require('@vue/preload-webpack-plugin');

**响应格式**：
\`\`\`xml
${this.responseHandler.generateFormatExample('fix_result', '修复后的完整配置文件内容', '详细说明所做的配置修改和原因')}
\`\`\`

请提供完整的修复后配置文件内容。`;
  }

  /**
   * 获取Vue文件修复模板
   */
  getVueFixTemplate() {
    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家。请修复以下 Vue 单文件组件的错误。

目标项目的技术栈是：Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**Vue 文件**: {filePath} (第 {fileIndex}/{totalFiles} 个文件)
**尝试次数**: 第 {attemptNumber} 次

**当前文件内容**：
\`\`\`vue
{fileContent}
\`\`\`

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**相关上下文文件**：
{contextFiles}

**上下文信息**：
{context}

**之前的尝试**：
{previousAttempts}

**响应格式**：
\`\`\`xml
${this.responseHandler.generateFormatExample('fix_result', '修复后的完整 Vue 文件内容', '详细说明 Vue 2 到 Vue 3 的迁移修改')}
\`\`\`

请提供完整的修复后 Vue 文件内容，确保符合 Vue 3 规范。`;
  }

  /**
   * 获取通用修复模板
   */
  getGeneralFixTemplate() {
    return `你是一个专业的 Vue 2 迁移到 Vue 3 的前端开发专家。请根据构建错误修复以下文件：

目标项目的技术栈是：Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**目标文件**: {filePath} (第 {fileIndex}/{totalFiles} 个文件)
**尝试次数**: 第 {attemptNumber} 次

**当前文件内容**：
\`\`\`
{fileContent}
\`\`\`

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**相关上下文文件**：
{contextFiles}

**上下文信息**：
{context}

**之前的尝试**：
{previousAttempts}

**修复原则**：
1. 请确保符合 Vue 3 的编码和实践方式。
2. 尽可能只关注在重写组件上。

**响应格式**：
\`\`\`xml
${this.responseHandler.generateFormatExample('fix_result', '修复后的完整文件内容', '简要说明所做的修改')}
\`\`\`

请提供完整的修复后文件内容。`;
  }

  /**
   * 获取运行时错误修复模板
   */
  getRuntimeErrorFixTemplate() {
    return `你是一个专业的 Vue 2 迁移 Vue 3 的运行时错误修复专家。请分析并修复以下运行时错误。

目标项目的技术栈是：Webpack 5 + Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**出错文件**: {fileName}
**错误位置**: 第 {lineNumber} 行，第 {columnNumber} 列
**错误类型**: {errorType}

**错误信息**：
\`\`\`
{errorMessage}
\`\`\`

**错误堆栈**：
\`\`\`
{errorStack}
\`\`\`

**Vue组件追踪**：
{componentTrace}

**当前文件内容**：
\`\`\`
{fileContent}
\`\`\`

**上下文信息**：
{context}

**定制修复指导**：
{customGuidance}

**响应格式**：(请严格按以下格式返回，方便我解析）

\`\`\`xml
${this.responseHandler.generateFormatExample('fix_result', '修复后的完整文件内容', '详细说明运行时错误的修复方案和原因')}
\`\`\`

请仔细分析运行时错误的根本原因，提供完整的修复后文件内容，确保修复后的代码能够正常运行且不会产生新的错误。`;
  }

  /**
   * 获取Webpack工具调用模板
   */
  getWebpackToolCallTemplate() {
    return `你是一个专业的 Webpack 配置专家。请分析以下 Webpack 配置错误，确定需要读取哪些相关文件来理解配置上下文。

目标项目的技术栈是：Webpack 5 + Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**配置文件**: {filePath}
**尝试次数**: 第 {attemptNumber} 次

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**可用工具**：
{toolsDescription}

**分析重点**：
- package.json 中的依赖版本
- 其他配置文件（babel.config.js, tsconfig.json 等）
- 入口文件和主要组件
- 插件和加载器配置

**响应格式**：
\`\`\`json
{
  "tool_calls": [
    {
      "name": "read_file",
      "parameters": {
        "file_path": "package.json"
      }
    }
  ],
  "reasoning": "需要检查依赖版本和配置"
}
\`\`\``;
  }

  /**
   * 获取Vue工具调用模板
   */
  getVueToolCallTemplate() {
    return `你是一个专业的 Vue 开发专家。请分析以下 Vue 文件错误，确定需要读取哪些相关文件来理解组件上下文。

目标项目的技术栈是：Webpack 5 + Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**Vue 文件**: {filePath}
**尝试次数**: 第 {attemptNumber} 次

**构建错误输出**：
\`\`\`
{buildOutput}
\`\`\`

**可用工具**：
{toolsDescription}

**分析重点**：
- 父组件和子组件
- 相关的工具函数和类型定义
- 路由配置
- 状态管理文件

**响应格式**：
\`\`\`json
{
  "tool_calls": [
    {
      "name": "read_file",
      "parameters": {
        "file_path": "src/router/index.js"
      }
    }
  ],
  "reasoning": "需要检查路由配置和组件依赖"
}
\`\`\``;
  }

  // 辅助方法
  truncateOutput(output) {
    /// remove all [@vue/compiler-sfc] ::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead of ::v-deep <inner-selector>.
    let lines = output.split('\n');
    lines = lines.filter(line => !line.includes('@vue/compiler-sfc'));
    output = lines.join('\n');

    if (output.length <= this.options.maxOutputLength) {
      return output;
    }

    return output.substring(0, this.options.maxOutputLength) + '\n... (输出已截断)';
  }

  formatContext(context) {
    if (!context || Object.keys(context).length === 0) {
      return '无额外上下文信息';
    }

    return Object.entries(context)
      .map(([key, value]) => `- ${key}: ${value}`)
      .join('\n');
  }

  formatContextFiles(contextFiles) {
    if (!contextFiles || Object.keys(contextFiles).length === 0) {
      return '无相关上下文文件';
    }

    return Object.entries(contextFiles)
      .map(([filePath, content]) => {
        const truncatedContent = content.length > 1000
          ? content.substring(0, 1000) + '\n... (内容已截断)'
          : content;
        return `**${filePath}**:\n\`\`\`\n${truncatedContent}\n\`\`\``;
      })
      .join('\n\n');
  }

  formatPreviousAttempts(attempts) {
    if (!attempts || attempts.length === 0) {
      return '这是第一次尝试';
    }

    return attempts.map((attempt, index) =>
      `尝试 ${index + 1}: ${attempt.error || attempt.description || '修复失败'}`
    ).join('\n');
  }

  formatComponentTrace(componentTrace) {
    if (!componentTrace || componentTrace.length === 0) {
      return '无组件追踪信息';
    }

    return componentTrace.map((trace, index) => {
      const name = trace.name || 'Anonymous';
      const file = trace.file || 'unknown';
      const line = trace.line || 0;
      return `${index + 1}. ${name} (${file}:${line})`;
    }).join('\n');
  }
}

module.exports = PromptBuilder;
